package com.wexl.erp.medical.repository;

import com.wexl.erp.medical.model.ErpMedicalHistory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MedicalRepository extends JpaRepository<ErpMedicalHistory, Long> {
  Optional<ErpMedicalHistory> findByStudentId(Long studentId);

  List<ErpMedicalHistory> findAllByOrgSlug(String orgSlug);
}
