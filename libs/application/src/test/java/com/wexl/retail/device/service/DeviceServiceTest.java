package com.wexl.retail.device.service;

import static org.junit.jupiter.api.Assertions.*;

import com.wexl.retail.device.dto.UserDeviceInfoRequest;
import java.util.List;
import org.junit.jupiter.api.Test;

class DeviceServiceTest {

  @Test
  void appUsingOldVersionAndLatestVersionAvailableWeXLSchool() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setDeviceModel("Android");
    request.setAppVersion("1.0.74");
    request.setAppPackageName("com.wexledu.mobile.school");

    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.0.76");
    deviceService.setUpdatableSchoolApps(
        List.of("com.wexledu.mobile.school", "com.wexledu.mobile.school.dev"));
    assertTrue(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }

  @Test
  void appUsingLatestVersionWeXLSchool() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setDeviceModel("Android");
    request.setAppVersion("1.0.27");
    request.setAppPackageName("com.wexledu.mobile.school");

    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.0.25");
    assertFalse(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }

  @Test
  void invalidMinimumAppVersion() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setDeviceModel("Android");
    request.setAppVersion("1.0.25");
    request.setAppPackageName("com.wexledu.mobile.school");

    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.27"); // Invalid version
    assertFalse(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }

  @Test
  void oldVersionsWhichDontSupportUpgrade() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setDeviceModel("Android");
    request.setAppVersion("1.0.18");
    request.setAppPackageName("com.wexledu.mobile.school");

    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.0.27"); // Invalid version
    assertFalse(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }

  @Test
  void deviceAppVersionNotAvaialable() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setAppVersion(null);
    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.0.27");
    assertFalse(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }

  @Test
  void appUsingOldVersionAndLatestVersionAvailableIstaApp() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setDeviceModel("Android");
    request.setAppVersion("1.0.25");
    request.setAppPackageName("com.wexledu.mobile.school.ista");

    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.0.27");
    assertFalse(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }

  @Test
  void isUpdateAvaialableForiPhone() {
    UserDeviceInfoRequest request = new UserDeviceInfoRequest();
    request.setDeviceModel("iPhone");
    request.setAppVersion("1.0.25");

    DeviceService deviceService = new DeviceService();
    deviceService.setMinimumAppVersion("1.0.27");
    assertFalse(deviceService.isAppUpdateAvailable(request, "harper10").getStatus());
  }
}
