package com.wexl.retail.security.utils;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.wexl.retail.commons.security.utils.AccessTokenProcessor;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.Authentication;

class AccessTokenProcessorTest {

  @Test
  void authenticate() {
    String token =
        """
        eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MjM2NTEyLCJmaXJzdE5hbWUiOiJTYWkgVGVqYSIsImxhc3ROYW\
        1lIjoiUmVkZHkiLCJvcmdhbml6YXRpb24iOiJ3ZXhsLWdpdGFuamFsaSIsInNjb3BlIjoiM0RfVklERU9TIEFDVE\
        lWSVRZX0ZFRUQgQUNUSVZJVFlfVklERU9TIEFOTk9VTkNFTUVOVFMgQVBQIEFQUF9DRU5URVIgQVJWUiBBU1NJR0\
        5NRU5UIEJBREdFUyBDTEFTU1JPT01fU1RVREVOVCBDT01QRVRJVElWRV9FWEFNUyBDT05DRVBUX1ZJREVPUyBDT0\
        5DRVBUX1ZJREVPU19ISU5ESV9NRURJVU0gRUJPT0tTIEVYUEVDVF9FWEFNIElOVEVSQUNUSVZFQ09OVEVOVCBLTk\
        9XTEVER0VNRVRFUiBMSVZFX0NMQVNTRVMgTUFOQUdFX0FTU0VUUyBNQU5BR0VfQ09VUlNFUyBNQU5BR0VfUFJPR1\
        JBTSBNWV9BQ1RJVklUWSBNWUtOT1dMRURHRSBNWV9WSURFT1MgUEVORElOR19FWEFNX0VWQUxVQVRJT04gUEVQS0\
        lEWl9MRUFSTk1BVEUgUFJBQ1RJQ0UgUFVEQU1JX1dPUktTSEVFVFMgUkVGRVJSQUwgUkVNRURJQVRJT04gUkVTT1\
        VSQ0VTIFNDSEVEVUxFRF9URVNUIFNJTVVMQVRJT04gU1BFQ0lBTF9FWEFNIFNZTk9QU0lTIFRFU1QgVEVTVF9DRU\
        5UUkUgVElNRV9TUEVOVCBWSURFT1MgV0VYTCBXSFlfVklERU9TIFdPUktTSEVFVFMiLCJ6b25lIjoiUFJPRCIsIm\
        xvZ2luTWV0aG9kIjoidXNlcm5hbWVfcGFzc3dvcmQiLCJzdWIiOiJnaXRhbmphbGkyMTAwMiIsImp0aSI6IjIzNj\
        UxMiIsImlhdCI6MTY4MzEwMjMwMiwiZXhwIjoxNzg4Mjg2MzAyLCJndWlkIjoiNTk4MzY4MzItMzNiMC00MmI4LT\
        k0ZjItMWM0M2M1NDkzYzk5Iiwic2Nob29sTmFtZSI6IldlWEwgR2l0YW5qYWxpIiwiZ3JhZGUiOiJFaWdodGggR3\
        JhZGUgLSBWSUlJIiwiY2xhc3NJZCI6NCwic3R1ZGVudElkIjoyMzY1MTMsImJvYXJkSWQiOjEsInBhcmVudElkI\
        joyMzY1MTAsImhhc1ByZW1pdW1TdWJzY3JpcHRpb24iOnRydWUsInJvbGVzIjpbIlJPTEVfSVNUVURFTlQiXSwie\
        E9yZ3MiOlsiYW1yOTc3Il0sIm1vYmlsZU51bWJlciI6Ijk5NDk5MDU5NzQifQ.VuFBI-tiGnf9WjCTPgafzw5YXo\
        2ChnVVhl1ab7oMnuA\
        """;

    AccessTokenProcessor accessTokenProcessor =
        new AccessTokenProcessor(
            "VGhpc0lzQUdyZWF0U2VjcmV0VG9LZWVwVGhpc0lzQUdyZWF0U2VjcmV0VG9LZWVwVGhpc0lzQUdyZWF0U2VjcmV0VG9LZWVwVGhpc0lzQUdyZWF0U2VjcmV0VG9LZWVw",
            new String[] {"<EMAIL>"});
    Authentication authenticate = accessTokenProcessor.authenticate(token);

    assertThat(authenticate.getAuthorities()).hasSize(1);
    assertThat(authenticate.getAuthorities().toString()).contains("ROLE_ISTUDENT");
  }
}
