package com.wexl.retail.util;

import org.junit.jupiter.api.Test;

class CryptoUtilsTest {

  @Test
  public void encryptTest() {
    String decryptedString =
        "PN4vJBBtr1E6zGjD/Di/CQ==:25f31564122a4e97:nrslPuNT2HcuIXvydh3QeTTFatMfMKDO5VWhggeI09LiKMpR1nEDUoVxTbetUglwbwfdtBv9mqU4QBA4zvA5mfWpaKpFS20Ul2FXYYjscB9vrcU06xBigG74yayl4MqKmEzkJTYJOBr7Pe20qzZslJDjK+AOj3C6LAYa8k6oB/I=";

    CryptoUtils utils = new CryptoUtils();
    utils.setSecret("12345");
    utils.decrypt(decryptedString);
  }
}
