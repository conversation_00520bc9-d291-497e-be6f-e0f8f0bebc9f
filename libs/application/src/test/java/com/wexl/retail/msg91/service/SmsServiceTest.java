package com.wexl.retail.msg91.service;

import static org.junit.jupiter.api.Assertions.*;

import com.wexl.retail.msg91.dto.Msg91Dto.Recipient;
import java.util.List;
import org.junit.jupiter.api.Test;

class SmsServiceTest {

  @Test
  public void testValidPhoneNumbers() {
    SmsService service = new BasicSmsService();
    final List<Recipient> recipients =
        service.filterInvalidPhoneNumbers(
            List.of(
                Recipient.builder().mobiles("+911234567890").build(),
                Recipient.builder().mobiles("0123456789").build(),
                Recipient.builder().mobiles("123456").build()));
    assertEquals(2, recipients.size());
    assertEquals("911234567890", recipients.get(0).mobiles());
    assertEquals("910123456789", recipients.get(1).mobiles());
  }

  static class BasicSmsService implements SmsService {

    @Override
    public void sendBulkMessage(String templateId, List<Recipient> recipients) {}
  }
}
