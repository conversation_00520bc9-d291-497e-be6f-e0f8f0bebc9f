package com.wexl.retail.user.service;

import static com.wexl.retail.user.service.GenericUserPasswordService.UUID_REGEX;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class GenericUserPasswordServiceTest {

  @Test
  public void ensureUserNameMatchesRegex() {

    assertTrue("ebee8565-99c2-41ba-821c-235b9822c3ee".matches(UUID_REGEX));
    assertFalse("gitanjali21002".matches(UUID_REGEX));
  }
}
