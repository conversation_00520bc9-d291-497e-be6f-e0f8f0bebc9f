package com.wexl.retail.util;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import jakarta.annotation.PostConstruct;
import java.util.Optional;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Data
@Component
public class WexlTokenHelper {
  @Autowired private UserRepository userRepository;
  private String childOneToken;
  private String teacherToken;
  private String studentToken;

  @Autowired private AuthService authService;

  @PostConstruct
  public void init() {
    this.childOneToken = getStudentAccessToken();
    this.teacherToken = getTeacherAccessToken();
    this.studentToken = this.childOneToken;
  }

  private User getUserByEmail(String email) {
    Optional<User> byEmail = userRepository.findUserByEmail(email);
    if (byEmail.isEmpty()) {
      throw new IllegalArgumentException("Invalid Email for user in the database [" + email + "]");
    }
    return byEmail.get();
  }

  private String getStudentAccessToken() {
    String username = "amar1002";
    Optional<User> possibleUser = userRepository.findByAuthUserId(username);
    if (possibleUser.isEmpty()) {
      throw new IllegalArgumentException(
          "Invalid username for user in the database [" + username + "]");
    }
    return authService.generateStudentAccessToken(
        false, possibleUser.get(), LoginMethod.SYSTEM_CREATED);
  }

  private String getTeacherAccessToken() {
    User teacher = getUserByEmail("<EMAIL>");
    return authService.generateTeacherAccessToken(false, teacher, LoginMethod.SYSTEM_CREATED);
  }
}
