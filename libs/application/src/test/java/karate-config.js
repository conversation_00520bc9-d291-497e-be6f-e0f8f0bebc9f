function fn() {
  var env = karate.env; // get system property 'karate.env'
  karate.log('karate.env system property was:', env);
  if (!env) {
    env = 'dev';
  }
  var config = {
    env: env,
    myVarName: 'someValue'
  }
  config.currentTime = function () {
             return "" + new Date().getTime();
             }
      config.endTime = function () {
          return "" + new Date().setMinutes(5);
      }

         config.expiryDate = function () {
            return "" + new Date().setSeconds(5);
         }
  if (env == 'dev') {
    // customize
    // e.g. config.foo = 'bar';
    config.baseUrl = 'https://learn.academyteacher.com/api';
    config.student1Username='gitanjali21002';
    config.student1Password='password@123';
    config.teacher1Username='<EMAIL>';
    config.teacher1Password='password@123';
    config.teacher1AuthId='6bb552ec-8520-4716-814a-c31eb876025e';
    config.testDefinitionId='5050289';
    config.worksheetDefinitionId='5090414'
    config.chapterId='61'
    config.assignmentDefinitionId='5174715';
    config.student1Id='236512';
    config.teacher1Id='236521';
    config.student1UserId='236513';
    config.teacher1UserId='236522';
    config.classroomId='4';
    config.scheduleId='163';
    config.meetingRoomId='6';
    config.taskId='67';
    config.taskInstId='117';
    config.classroomPractiseTaskId='77';
    config.orgName1='wexl-gitanjali';

  } else if (env == 'prod') {
    // customize
    config.baseUrl = 'https://console.wexledu.com/api';
   config.student1Username='gitanjali21002';
       config.student1Password='password@123';
       config.teacher1Username='<EMAIL>';
       config.teacher1Password='password@123';
       config.teacher1AuthId='6bb552ec-8520-4716-814a-c31eb876025e';
       config.testDefinitionId='5050289';
       config.worksheetDefinitionId='5090414';
       config.chapterId='61';
       config.assignmentDefinitionId='5174715';
       config.student1Id='236512';
       config.teacher1Id='236521';
       config.student1UserId='236513';
       config.teacher1UserId='236522';
       config.classroomId='4';
        config.scheduleId='161';
        config.meetingRoomId='6';
        config.taskId='67';
        config.taskInstId='117';
        config.classroomPractiseTaskId='77';
         config.orgName1='wexl-gitanjali';
  }
  return config;
}
