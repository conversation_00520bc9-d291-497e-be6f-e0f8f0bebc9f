<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="202409010528" author="Bharat">
    <createIndex indexName="idx_classroom_teachers_classroom_id" tableName="classroom_teachers">
      <column name="classroom_id"/>
    </createIndex>
  </changeSet>
  <changeSet id="202409010551" author="Bharat">
    <createIndex indexName="idx_student_registration_codes_code" tableName="student_registration_codes">
      <column name="code"/>
    </createIndex>
    <createIndex indexName="idx_test_schedule_knowledge_details_test_schedule_knowledge_id" tableName="test_schedule_knowledge_knowledge_details">
      <column name="test_schedule_knowledge_id"/>
    </createIndex>
    <createIndex indexName="idx_notification_sections_notification_id" tableName="notification_sections">
      <column name="notification_id"/>
    </createIndex>
    <createIndex indexName="idx_test_definition_sections_test_definition_id" tableName="test_definition_sections">
      <column name="test_definition_id"/>
    </createIndex>
    <createIndex indexName="idx_student_relationship_student_a_id" tableName="student_relationship">
      <column name="student_a_id"/>
    </createIndex>
    <createIndex indexName="idx_student_relationship_student_b_id" tableName="student_relationship">
      <column name="student_b_id"/>
    </createIndex>
    <createIndex indexName="idx_calender_details_date_id" tableName="calender_details">
      <column name="date_id"/>
    </createIndex>
    <createIndex indexName="idx_document_students_document_id" tableName="document_students">
      <column name="document_id"/>
    </createIndex>
    <createIndex indexName="idx_country_codes_code" tableName="country_codes">
      <column name="code"/>
    </createIndex>
    <createIndex indexName="idx_classroom_students_classroom_id" tableName="classroom_students">
      <column name="classroom_id"/>
    </createIndex>
    <createIndex indexName="idx_classroom_students_student_id" tableName="classroom_students">
      <column name="student_id"/>
    </createIndex>
    <createIndex indexName="idx_classrooms_name" tableName="classrooms">
      <column name="name"/>
    </createIndex>
    <createIndex indexName="idx_guardians_student_id" tableName="guardians">
      <column name="student_id"/>
    </createIndex>
    <createIndex indexName="idx_course_module_course_definition_id" tableName="course_module">
      <column name="course_definition_id"/>
    </createIndex>
    <createIndex indexName="idx_role_templates_global_profile_id" tableName="role_templates">
      <column name="global_profile_id"/>
    </createIndex>
    <createIndex indexName="idx_role_templates_slug" tableName="role_templates">
      <column name="slug"/>
    </createIndex>
    <createIndex indexName="idx_document_teachers_teacher_id" tableName="document_teachers">
      <column name="teacher_id"/>
    </createIndex>
    <createIndex indexName="idx_course_categories_org_id" tableName="course_categories">
      <column name="org_id"/>
    </createIndex>
    <createIndex indexName="idx_threads_created_by" tableName="threads">
      <column name="created_by"/>
    </createIndex>
    <createIndex indexName="idx_threads_teacher_id" tableName="threads">
      <column name="teacher_id"/>
    </createIndex>
    <createIndex indexName="idx_speech_tasks_speech_ref" tableName="speech_tasks">
      <column name="speech_ref"/>
    </createIndex>
    <createIndex indexName="idx_mlp_org_slug_section_id" tableName="mlp">
      <column name="org_slug"/>
      <column name="section_id"/>
    </createIndex>
    <createIndex indexName="idx_mlp_chapter_slug" tableName="mlp">
      <column name="chapter_slug"/>
    </createIndex>
    <createIndex indexName="idx_mlp_exam_ref" tableName="mlp">
      <column name="exam_ref"/>
    </createIndex>
    <createIndex indexName="idx_mlp_parent_mlp_id" tableName="mlp">
      <column name="parent_mlp_id"/>
    </createIndex>
    <createIndex indexName="idx_mlp_subtopic_slug" tableName="mlp">
      <column name="subtopic_slug"/>
    </createIndex>
    <createIndex indexName="idx_mlp_section_id_subject_slug" tableName="mlp">
      <column name="section_id"/>
      <column name="subject_slug"/>
    </createIndex>
    <createIndex indexName="idx_teacher_sections_section_id" tableName="teacher_sections">
      <column name="section_id"/>
    </createIndex>
    <createIndex indexName="idx_teacher_subjects_section_id_subject_slug_board_slug" tableName="teacher_subjects">
      <column name="section_id"/>
      <column name="subject_slug"/>
      <column name="board_slug"/>
    </createIndex>
    <createIndex indexName="idx_teacher_subjects_teacher_id" tableName="teacher_subjects">
      <column name="teacher_id"/>
    </createIndex>
    <createIndex indexName="idx_classroom_schedules_classroom_id" tableName="classroom_schedules">
      <column name="classroom_id"/>
    </createIndex>
    <createIndex indexName="idx_calender_events_section_uuid" tableName="calender_events">
      <column name="section_uuid"/>
    </createIndex>
    <createIndex indexName="idx_zero_digital_chapter_slug" tableName="zero_digital">
      <column name="chapter_slug"/>
    </createIndex>
    <createIndex indexName="idx_zero_digital_section_uuid_subject_slug_grade_slug_board_slug" tableName="zero_digital">
      <column name="section_uuid"/>
      <column name="subject_slug"/>
      <column name="grade_slug"/>
      <column name="board_slug"/>
    </createIndex>
    <createIndex indexName="idx_zero_digital_subject_slug_org_slug_grade_slug" tableName="zero_digital">
      <column name="subject_slug"/>
      <column name="org_slug"/>
      <column name="grade_slug"/>
    </createIndex>
    <createIndex indexName="idx_teacher_orgs_teacher_id" tableName="teacher_orgs">
      <column name="teacher_id"/>
    </createIndex>
    <createIndex indexName="idx_schedule_inst_attendance_classroom_schedule_inst_id" tableName="schedule_inst_attendance">
      <column name="classroom_schedule_inst_id"/>
    </createIndex>
    <createIndex indexName="idx_section_schedule_section_id" tableName="section_schedule">
      <column name="section_id"/>
    </createIndex>
    <createIndex indexName="idx_offline_test_schedule_offline_test_definition_id" tableName="offline_test_schedule">
      <column name="offline_test_definition_id"/>
    </createIndex>
    <createIndex indexName="idx_notifications_teacher_id_org_slug" tableName="notifications">
      <column name="teacher_id"/>
      <column name="org_slug"/>
    </createIndex>
    <createIndex indexName="idx_students_knowledge_meter_student_id_chapter_slug" tableName="students_knowledge_meter">
      <column name="student_id"/>
      <column name="chapter_slug"/>
    </createIndex>
    <createIndex indexName="idx_students_knowledge_meter_student_id_sub_topic_slug" tableName="students_knowledge_meter">
      <column name="student_id"/>
      <column name="sub_topic_slug"/>
    </createIndex>
    <createIndex indexName="idx_students_knowledge_meter_student_id_subject_slug" tableName="students_knowledge_meter">
      <column name="student_id"/>
      <column name="subject_slug"/>
    </createIndex>
    <createIndex indexName="idx_subject_profiles_details_subject_slug_board_slug_grade_slug" tableName="subject_profiles_details">
      <column name="subject_slug"/>
      <column name="board_slug"/>
      <column name="grade_slug"/>
    </createIndex>
    <createIndex indexName="idx_sections_organization_board_slug_grade_slug" tableName="sections">
      <column name="organization"/>
      <column name="board_slug"/>
      <column name="grade_slug"/>
    </createIndex>
    <createIndex indexName="idx_sections_organization_grade_id" tableName="sections">
      <column name="organization"/>
      <column name="grade_id"/>
    </createIndex>
    <createIndex indexName="idx_subject_metadata_name" tableName="subject_metadata">
      <column name="name"/>
    </createIndex>
    <createIndex indexName="idx_subject_metadata_grade_slug_org_slug_board_slug" tableName="subject_metadata">
      <column name="grade_slug"/>
      <column name="org_slug"/>
      <column name="board_slug"/>
    </createIndex>
    <createIndex indexName="idx_subject_metadata_wexl_subject_slug_grade_slug_org_slug" tableName="subject_metadata">
      <column name="wexl_subject_slug"/>
      <column name="grade_slug"/>
      <column name="org_slug"/>
    </createIndex>
    <createIndex indexName="idx_offline_test_schedule_student_attendance_student_id" tableName="offline_test_schedule_student_attendance">
      <column name="student_id"/>
    </createIndex>
    <createIndex indexName="idx_offline_test_schedule_student_attendance_offline_test_definition_id" tableName="offline_test_schedule_student_attendance">
      <column name="offline_test_definition_id"/>
    </createIndex>
    <createIndex indexName="idx_report_card_templates_org_slug_report_card_template_type" tableName="report_card_templates">
      <column name="org_slug"/>
      <column name="report_card_template_type"/>
    </createIndex>
    <createIndex indexName="idx_student_attribute_values_student_id_attribute_definition_id" tableName="student_attribute_values">
      <column name="student_id"/>
      <column name="attribute_definition_id"/>
    </createIndex>
    <createIndex indexName="idx_qp_gen_blueprints_org_slug" tableName="qp_gen_blueprints">
      <column name="org_slug"/>
    </createIndex>
    <createIndex indexName="idx_message_templates_category_org_slug_category" tableName="message_templates_category">
      <column name="org_slug"/>
      <column name="category"/>
    </createIndex>
    <createIndex indexName="idx_message_templates_category_org_slug_type" tableName="message_templates_category">
      <column name="org_slug"/>
      <column name="type"/>
    </createIndex>
  </changeSet>
  <changeSet id="202409012204" author="Bharat">
    <createIndex indexName="idx_test_definitions_organization_grade_slug" tableName="test_definitions">
      <column name="organization"/>
      <column name="grade_slug"/>
    </createIndex>
    <createIndex indexName="idx_pdf_drawing_positions_annotation_id" tableName="pdf_drawing_positions">
      <column name="annotation_id"/>
    </createIndex>
    <createIndex indexName="idx_exams_schedule_test_id_student_id" tableName="exams">
      <column name="schedule_test_id"/>
      <column name="student_id"/>
    </createIndex>
    <createIndex indexName="idx_syllabus_trackings_board_slug_org_slug_grade_slug" tableName="syllabus_trackings">
      <column name="board_slug"/>
      <column name="org_slug"/>
      <column name="grade_slug"/>
    </createIndex>
    <createIndex indexName="idx_section_attendance_details_section_attendance_id" tableName="section_attendance_details">
      <column name="section_attendance_id"/>
    </createIndex>
    <createIndex indexName="idx_subject_metadata_students_student_id" tableName="subject_metadata_students">
      <column name="student_id"/>
    </createIndex>
  </changeSet>
  <changeSet id="202409040957" author="Jayanth">
    <createIndex tableName="offline_test_schedule_student" indexName="idx_offline_test_schedule_id_student_id">
      <column name="student_id"/>
      <column name="offline_test_schedule_id"/>
    </createIndex>
  </changeSet>
  <changeSet id="202409012208" author="Bharat">
    <sql>
      CREATE INDEX exams_student_id_idx ON public.exams USING btree (student_id) WHERE (test_definition_id IS NOT NULL);
    </sql>
    <rollback>
      <sql>
        DROP INDEX public.exams_student_id_idx;
      </sql>
    </rollback>
  </changeSet>



</databaseChangeLog>
