<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="202409102226" author="Jayanth">
        <createIndex tableName="test_schedule_knowledge" indexName="idx_test_schedule_knowledge_subject_slug_grade_slug_board">
            <column name="subject_slug"/>
            <column name="grade_slug"/>
            <column name="board"/>
        </createIndex>
    </changeSet>
    <changeSet id="202409161025" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists
                    tableName="notifications"
                    indexName="notifications_created_at_index"/>
        </preConditions>
        <dropIndex
                tableName="notifications"
                indexName="notifications_created_at_index"/>
    </changeSet>
    <changeSet id="202409161026" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists
                    tableName="mlp_inst"
                    indexName="mlp_inst_mlp_id_student_id_index"/>
        </preConditions>
        <dropIndex
                tableName="mlp_inst"
                indexName="mlp_inst_mlp_id_student_id_index"/>
    </changeSet>
    <changeSet id="202409161027" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists
                    tableName="mlp_attendance"
                    indexName="mlp_attendance_mlp_date_index"/>
        </preConditions>
        <dropIndex
                tableName="mlp_attendance"
                indexName="mlp_attendance_mlp_date_index"/>
    </changeSet>
    <changeSet id="202409161028" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists
                    tableName="test_questions"
                    indexName="test_questions_test_definition_id_index"/>
        </preConditions>
        <dropIndex
                tableName="test_questions"
                indexName="test_questions_test_definition_id_index"/>
    </changeSet>
    <changeSet id="202409161029" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists
                    tableName="sections"
                    indexName="sections_deleted_at_index"/>
        </preConditions>
        <dropIndex
                tableName="sections"
                indexName="sections_deleted_at_index"/>
    </changeSet>
    <changeSet id="202409161030" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists tableName="tasks" indexName="tasks_subtopic_name_org_slug_idx"/>
        </preConditions>
        <dropIndex
                tableName="tasks"
                indexName="tasks_subtopic_name_org_slug_idx"/>
    </changeSet>
    <changeSet id="202409111130" author="Jayanth">
        <preConditions onFail="MARK_RAN">
            <indexExists
                    tableName="exams"
                    indexName="ttt_exam_start_time" />
        </preConditions>
        <dropIndex
                tableName="exams"
                indexName="ttt_exam_start_time" />
    </changeSet>

</databaseChangeLog>