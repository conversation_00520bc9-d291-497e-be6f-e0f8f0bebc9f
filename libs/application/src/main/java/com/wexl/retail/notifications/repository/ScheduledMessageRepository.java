package com.wexl.retail.notifications.repository;

import com.wexl.retail.notifications.model.ScheduledMessage;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduledMessageRepository extends JpaRepository<ScheduledMessage, Long> {

  List<ScheduledMessage> findBySentFalseAndScheduledTimeBefore(LocalDateTime now);
}
