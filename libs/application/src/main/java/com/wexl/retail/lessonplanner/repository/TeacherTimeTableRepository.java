package com.wexl.retail.lessonplanner.repository;

import com.wexl.retail.lessonplanner.model.TeacherTimeTable;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TeacherTimeTableRepository extends JpaRepository<TeacherTimeTable, Long> {
  Optional<TeacherTimeTable> findByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuid(
      String orgSlug, String boardSlug, String gradeSlug, String sectionUuid);
}
