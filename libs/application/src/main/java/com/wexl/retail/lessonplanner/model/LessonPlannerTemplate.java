package com.wexl.retail.lessonplanner.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "lesson_planner_templates")
public class LessonPlannerTemplate extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "name")
  private String name;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "lessonPlannerTemplate", cascade = CascadeType.ALL)
  private List<LessonPlannerTemplateField> lessonPlannerTemplateFields;
}
