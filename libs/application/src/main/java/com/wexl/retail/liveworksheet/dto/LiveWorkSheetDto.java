package com.wexl.retail.liveworksheet.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.test.school.domain.TestType;
import java.util.List;
import lombok.Builder;

public record LiveWorkSheetDto() {

  @Builder
  public record LiveWorkSheetResponse(
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("test_name") String testName,
      @JsonProperty("test_state") String testState,
      @JsonProperty("schedule_test_id") long scheduleTestId,
      @JsonProperty("schedule_test_state") String scheduleTestState,
      @JsonProperty("schedule_test_uuid") String scheduleTestUuid,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("start_date") long startDate,
      @JsonProperty("end_date") long endDate,
      @JsonProperty("test_type") TestType testType,
      String status) {}

  @Builder
  public record ImageQuestionResponse(String path, String url, String previewUrl) {}

  @Builder
  public record LiveWorkSheetQuestionResponse(
      @JsonProperty("questions") List<Question> workSheetQuestion,
      List<LiveWorksheetAnswers> answers) {}

  @Builder
  public record LiveWorksheetAnswers(
      String answer,
      @JsonProperty("answer_uuid") String answerUuid,
      @JsonProperty("answer_type") WorkSheetQuestionType answerType) {}

  @Builder
  public record LiveWorksheetResult(
      LiveWorkSheetQuestionResponse liveWorkSheetQuestionResponse, String testName, String theme) {}
}
