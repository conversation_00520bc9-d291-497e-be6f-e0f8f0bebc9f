package com.wexl.retail.globalprofile.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalProfileRequest;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalProfileResponse;
import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.globalprofile.service.GlobalProfileService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@IsOrgAdmin
@RequiredArgsConstructor
@RestController
public class GlobalProfileController {

  private final GlobalProfileService globalProfileService;

  @GetMapping("/orgs/wexl-internal/global-profiles")
  public List<GlobalProfileResponse> getAllGlobalProfiles() {
    return globalProfileService.getAllProfiles();
  }

  @PostMapping("/orgs/wexl-internal/global-profiles")
  public void createGlobalProfile(@Valid @RequestBody GlobalProfileRequest globalProfileRequest) {
    globalProfileService.saveProfile(globalProfileRequest);
  }

  @GetMapping("/orgs/wexl-internal/global-profiles/{id}")
  public GlobalProfile getGlobalProfileById(@PathVariable Long id) {
    return globalProfileService.getGlobalProfile(id);
  }

  @PutMapping("/orgs/wexl-internal/global-profiles/{id}")
  public void updateGlobalProfile(
      @PathVariable Long id, @Valid @RequestBody GlobalProfileRequest globalProfileRequest) {
    globalProfileService.updateProfile(id, globalProfileRequest);
  }

  @DeleteMapping("/orgs/wexl-internal/global-profiles/{id}")
  public void deleteGlobalProfile(@PathVariable Long id) {
    // We wont' support this now still we are stable. Fix it later
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DeleteGlobalProfile");
  }

  @GetMapping("/orgs/{orgSlug}/global-profiles")
  public GlobalProfileResponse getGlobalProfileByOrg(@PathVariable String orgSlug) {
    return globalProfileService.getGlobalProfileByOrg(orgSlug);
  }
}
