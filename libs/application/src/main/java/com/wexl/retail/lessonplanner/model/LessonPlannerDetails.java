package com.wexl.retail.lessonplanner.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "lesson_planner_details")
public class LessonPlannerDetails extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lesson_planner_template_field_id")
  private LessonPlannerTemplateField lessonPlannerTemplateField;

  @Column(name = "definition", columnDefinition = "TEXT")
  private String definition;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lesson_planner_id")
  private LessonPlanner lessonPlanner;
}
