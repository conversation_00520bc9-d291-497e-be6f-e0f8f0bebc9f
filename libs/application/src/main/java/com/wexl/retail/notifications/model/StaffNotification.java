package com.wexl.retail.notifications.model;

import com.wexl.retail.model.User;
import com.wexl.retail.staff.model.Staff;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "notification_staff")
public class StaffNotification {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "notification_id")
  private Notification notification;

  @ManyToOne
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne
  @JoinColumn(name = "staff_id")
  private Staff staff;

  @Column(name = "org_slug")
  private String orgSlug;
}
