package com.wexl.retail.communications.happening.dto;

import com.wexl.retail.notifications.dto.NotificationDto;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record HappeningDto() {

  public record HappeningRequest(
      @NotNull String title, String message, List<String> attachment, List<String> link) {}

  @Builder
  public record HappeningResponse(
      List<NotificationDto.TeacherNotificationResponse> notifications) {}
}
