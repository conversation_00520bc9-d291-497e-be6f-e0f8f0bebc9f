package com.wexl.retail.communications.logbook.model;

import com.wexl.retail.communications.logbook.dto.LogBookType;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "log_book")
public class LogBook extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @Column(columnDefinition = "TEXT")
  private String description;

  @Column(name = "date_of_observation")
  private LocalDate dateOfObservation;

  @Column(name = "subject_activity")
  private String subjectActivity;

  @Column(name = "observer_name")
  private String observerName;

  @Column(name = "reported_to")
  private String reportedTo;

  @Column(name = "observed_behavior")
  private String observedBehavior;

  @Column(name = "interpretation")
  private String interpretation;

  @Column(name = "action_or_support")
  private String actionOrSupport;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "student_id")
  private Long studentId;

  @Type(JsonType.class)
  @Column(name = "attachments", columnDefinition = "jsonb")
  private List<String> attachments;

  @Column(name = "section_uuid")
  private String sectionUuid;

  @Enumerated(EnumType.STRING)
  private LogBookType type;

  @ManyToOne
  @JoinColumn(name = "teacher_id", nullable = false)
  private User teacher;
}
