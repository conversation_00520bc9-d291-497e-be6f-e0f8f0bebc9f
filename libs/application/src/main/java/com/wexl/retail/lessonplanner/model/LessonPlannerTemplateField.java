package com.wexl.retail.lessonplanner.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@Entity
@AllArgsConstructor
@RequiredArgsConstructor
@Table(name = "lesson_planner_template_fields")
public class LessonPlannerTemplateField extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "field_name")
  private String fieldName;

  @Column(name = "definition", columnDefinition = "TEXT")
  private String definition;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lesson_planner_template_id")
  private LessonPlannerTemplate lessonPlannerTemplate;
}
