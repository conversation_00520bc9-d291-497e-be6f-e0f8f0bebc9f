package com.wexl.retail.lessonplanner.repository;

import com.wexl.retail.lessonplanner.model.TeacherTimeTable;
import com.wexl.retail.lessonplanner.model.TeacherTimeTableDetail;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.section.domain.TeacherSubjects;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TeacherTimeTableDetailsRepository
    extends JpaRepository<TeacherTimeTableDetail, Long> {
  List<TeacherTimeTableDetail> findAllByTeacherTimeTableAndDateIn(
      TeacherTimeTable teacherTimeTable, List<LocalDate> currentWeekDates);

  List<TeacherTimeTableDetail> findAllByTeacherSubjectInAndDateIn(
      List<TeacherSubjects> teacherSubjects, List<LocalDate> currentWeekDates);

  List<TeacherTimeTableDetail> findAllByTeacherTimeTableAndDateBetween(
      TeacherTimeTable teacherTimeTable, LocalDate startDate, LocalDate endDate);

  @Query(
      value =
          """
                  select tttd.* from teacher_time_table_details tttd
                  join teacher_time_table ttt on ttt.id = tttd.teacher_time_table_id
                  join teacher_subjects ts on ts.id  = tttd.teacher_subject_id
                  where ttt.org_slug = :orgSlug and ttt.section_uuid in (:sections)
                  and tttd.date in (:dates) and ts.subject_slug = :subjectSlug
        """,
      nativeQuery = true)
  List<TeacherTimeTableDetail> getByOrgAndSectionAndDate(
      String orgSlug, List<LocalDate> dates, List<String> sections, String subjectSlug);

  List<TeacherTimeTableDetail> findAllByTeacherTimeTableAndDateInAndTeacherSubjectIn(
      TeacherTimeTable teacherTimeTable,
      List<LocalDate> currentWeekDates,
      List<TeacherSubjects> teacherSubjects);

  List<TeacherTimeTableDetail> findAllByTeacherTimeTableAndDateBetweenAndTeacherSubject_Teacher(
      TeacherTimeTable teacherTimeTable,
      LocalDate sourceFromDate,
      LocalDate sourceToDate,
      Teacher teacherInfo);

  List<TeacherTimeTableDetail> findAllByTeacherTimeTableAndDate(
      TeacherTimeTable teacherTimeTable, LocalDate targetDate);
}
