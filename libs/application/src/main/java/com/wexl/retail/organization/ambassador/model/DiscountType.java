package com.wexl.retail.organization.ambassador.model;

import lombok.Getter;

@Getter
public enum DiscountType {
  PERCENTAGE("percentage"),
  AMOUNT("fixed_amount");
  private String value;

  DiscountType(String value) {
    this.value = value;
  }

  public static DiscountType fromValue(String value) {
    for (DiscountType discountType : DiscountType.values()) {
      if (discountType.value.equals(value)) {
        return discountType;
      }
    }
    throw new IllegalArgumentException("Invalid discount type: " + value);
  }
}
