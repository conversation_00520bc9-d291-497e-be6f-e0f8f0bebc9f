package com.wexl.retail.studentfeedback.dto;

import com.wexl.retail.studentfeedback.model.FeedbackType;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class StudentFeedbackResponse {

  private Long id;
  private Long taskInst;
  private Long taskId;
  private Long feedbackDate;
  private FeedbackType type;
  private String message;
  private String subjectName;
  private String subjectSlug;
  private Long examId;
  private Float score;
  private String synopsisSlug;
  private String videoSlug;
  private String activityType;
  private String activityName;
  private String chapterName;
  private String chapterSlug;
  private String subtopicName;
  private String subtopicSlug;
  private Float totalMarks;
  boolean isExamCorrected;
  private List<StudentFeedbackDto.Response> feedBackMessage;
  private List<Long> feedBackId;
}
