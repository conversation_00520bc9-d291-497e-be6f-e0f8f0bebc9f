package com.wexl.retail.communications.circulars.service;

import com.wexl.retail.communications.circulars.dto.CircularsDto;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.UserRepository;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CircularsService {
  private final NotificationsService notificationService;
  private final NotificationRepository notificationRepository;
  private final UserRepository userRepository;
  private final EventNotificationService eventNotificationService;

  public void createCircularNotification(
      String orgSlug, CircularsDto.CircularRequest request, String teacherAuthId) {

    long createdAt =
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.message())
            .attachment(request.attachment())
            .link(request.link())
            .notificationType(NotificationType.ORGANIZATION)
            .orgSlugs(List.of(orgSlug))
            .feature(CommunicationFeature.CIRCULAR)
            .build();

    notificationService.createNotificationForOrganization(
        orgSlug, notificationRequest, teacherAuthId, true, createdAt);
    eventNotificationService.sendPushNotificationForOrganization(
        orgSlug, request.message(), request.title());
  }

  public CircularsDto.CircularResponse getCircularNotifications(String orgSlug) {
    var circularNotifications =
        notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
            orgSlug, CommunicationFeature.CIRCULAR);
    var notificationResponse =
        notificationService.buildNotificationResponse(circularNotifications, 100);
    return CircularsDto.CircularResponse.builder()
        .circularNotifications(notificationResponse)
        .build();
  }

  public void updateCircularNotification(
      String orgSlug, Long notificationId, CircularsDto.CircularRequest request) {
    var notification = notificationService.getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    notification.setTitle(request.title());
    notification.setMessage(request.message());
    notification.setAttachments(request.attachment());
    notification.setLink(request.link());
    notificationRepository.save(notification);
  }
}
