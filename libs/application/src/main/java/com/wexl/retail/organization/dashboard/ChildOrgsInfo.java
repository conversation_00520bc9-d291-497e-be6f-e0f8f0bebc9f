package com.wexl.retail.organization.dashboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ChildOrgsInfo {

  private String orgName;
  private String orgSlug;
  private List<String> boards;
  private Integer totalMlps;
  private Integer totalTeachers;
  private Integer totalStudents;
  private Long startDate;
  private String status;
  private Boolean isPublisher;
  private Boolean isParent;

  @JsonProperty("is_self_signup")
  private Boolean isSelfSignUp;
}
