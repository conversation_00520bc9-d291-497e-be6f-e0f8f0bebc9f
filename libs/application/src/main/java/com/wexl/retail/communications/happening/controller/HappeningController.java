package com.wexl.retail.communications.happening.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.communications.happening.dto.HappeningDto;
import com.wexl.retail.communications.happening.service.HappeningService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class HappeningController {
  private final HappeningService happeningService;

  @IsOrgAdminOrTeacher
  @PostMapping("/teachers/{teacherAuthUserId}/happenings")
  @ResponseStatus(HttpStatus.CREATED)
  public void createHappeningNotification(
      @RequestBody HappeningDto.HappeningRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {
    happeningService.createHappeningNotification(orgSlug, request, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/teachers/{teacherAuthId}/happenings/{notificationId}")
  public void updateHappeningNotification(
      @PathVariable String orgSlug,
      @PathVariable("notificationId") Long notificationId,
      @RequestBody HappeningDto.HappeningRequest request) {
    happeningService.updateHappeningNotification(orgSlug, notificationId, request);
  }

  @GetMapping("/happenings")
  public HappeningDto.HappeningResponse getHappeningNotification(@PathVariable String orgSlug) {
    return happeningService.getHappeningNotifications(orgSlug);
  }
}
