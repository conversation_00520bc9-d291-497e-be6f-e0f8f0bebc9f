package com.wexl.retail.communications.circulars.dto;

import com.wexl.retail.notifications.dto.NotificationDto;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record CircularsDto() {

  public record CircularRequest(
      @NotNull String title, String message, List<String> attachment, List<String> link) {}

  @Builder
  public record CircularResponse(
      List<NotificationDto.TeacherNotificationResponse> circularNotifications) {}
}
