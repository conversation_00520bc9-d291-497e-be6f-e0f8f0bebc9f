package com.wexl.retail.notifications.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "email_schedules")
public class ScheduledEmail {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "recipient_email")
  private String recipientEmail;

  @Column(name = "recipient_name")
  private String recipientName;

  private String subject;

  @Column(name = "message", columnDefinition = "TEXT")
  private String message;

  @Column(name = "scheduled_time")
  private LocalDateTime scheduledTime;

  private boolean sent;

  @Column(name = "attachment_url")
  private String attachmentUrl;
}
