package com.wexl.retail.notifications.service;

import com.wexl.retail.generic.ProfileUtils;
import com.wexl.retail.notifications.model.ScheduledEmail;
import com.wexl.retail.notifications.model.ScheduledMessage;
import com.wexl.retail.notifications.repository.ScheduledEmailRepository;
import com.wexl.retail.notifications.repository.ScheduledMessageRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.email.scheduler", havingValue = "true")
public class SendScheduledEmails {
  private final ScheduledEmailRepository scheduledEmailRepository;
  private final ProfileUtils profileUtils;
  private final EmailNotificationService emailNotificationService;
  private final ScheduledMessageRepository scheduledMessageRepository;

  @Value("${app.allowed.test.orgSlug:}")
  private String allowedTestOrgSlug;

  public void processScheduledEmails() {
    log.info("Started processing scheduled emails...");

    List<ScheduledEmail> dueEmails =
        scheduledEmailRepository.findBySentFalseAndScheduledTimeBefore(LocalDateTime.now());
    if (dueEmails.isEmpty()) {
      log.info("No scheduled emails to process at this time.");
      return;
    }
    log.info("Found {} scheduled emails to be processed", dueEmails.size());

    if (profileUtils.isDev()) {
      if (StringUtils.isBlank(allowedTestOrgSlug)) {
        log.warn("Dev profile active, but allowedTestOrgSlug is blank. Skipping email processing.");
        return;
      }
      log.info("filtering emails for allowed test orgSlug: {}", allowedTestOrgSlug);
      dueEmails =
          dueEmails.stream()
              .filter(email -> allowedTestOrgSlug.equals(email.getOrgSlug()))
              .collect(Collectors.toList());
      if (dueEmails.isEmpty()) {
        log.info("No scheduled emails for allowed test org: {}", allowedTestOrgSlug);
        return;
      }
    }

    emailNotificationService.processDueEmails(dueEmails);
    log.info("Finished processing {} scheduled emails", dueEmails.size());
  }

  public void processScheduledMessages() {
    log.info("Started processing scheduled messages...");

    List<ScheduledMessage> dueMessages =
        scheduledMessageRepository.findBySentFalseAndScheduledTimeBefore(LocalDateTime.now());

    if (dueMessages.isEmpty()) {
      log.info("No scheduled messages to process at this time.");
      return;
    }
    log.info("Found {} scheduled messages to be processed", dueMessages.size());

    if (profileUtils.isDev()) {
      if (StringUtils.isBlank(allowedTestOrgSlug)) {
        log.warn("Dev profile active, but allowedTestOrgSlug is blank. Skipping email processing.");
        return;
      }
      log.info("filtering emails for allowed test orgSlug: {}", allowedTestOrgSlug);
      dueMessages =
          dueMessages.stream()
              .filter(msg -> allowedTestOrgSlug.equals(msg.getOrgSlug()))
              .collect(Collectors.toList());
      if (dueMessages.isEmpty()) {
        log.info("No scheduled messages for allowed test org: {}", allowedTestOrgSlug);
        return;
      }
    }

    emailNotificationService.processDueMessages(dueMessages);
    log.info("Finished processing {} scheduled messages", dueMessages.size());
  }
}
