package com.wexl.retail.globalprofile.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalFeatureResponse;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.service.GlobalFeatureService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@IsOrgAdmin
@RequiredArgsConstructor
@RestController
@RequestMapping("/orgs/wexl-internal/global-features")
public class GlobalFeatureController {
  private final GlobalFeatureService globalFeatureService;

  @GetMapping
  public List<GlobalFeatureResponse> getAllFeatures(
      @RequestParam(required = false) AppTemplate role) {
    return globalFeatureService.getAllFeatures(role);
  }

  @PostMapping
  public void createGlobalFeature(
      @Valid @RequestBody GlobalProfileDto.GlobalFeatureRequest globalProfileRequest) {
    globalFeatureService.saveFeature(globalProfileRequest);
  }

  @GetMapping("/{id}")
  public GlobalFeatureResponse getGlobalFeature(@PathVariable Long id) {
    return globalFeatureService.getFeature(id);
  }

  @PutMapping("/{id}")
  public void updateGlobalFeature(
      @PathVariable Long id,
      @Valid @RequestBody GlobalProfileDto.GlobalFeatureRequest globalFeatureRequest) {
    globalFeatureService.updateFeature(id, globalFeatureRequest);
  }

  @DeleteMapping("/{id}")
  public void deleteGlobalFeature(@PathVariable Long id) {
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DeleteGlobalFeature");
  }
}
