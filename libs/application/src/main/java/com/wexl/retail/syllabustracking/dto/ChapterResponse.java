package com.wexl.retail.syllabustracking.dto;

import java.util.List;

public interface ChapterResponse {

  String getSubjectName();

  String getSubjectSlug();

  Long getTotalChapters();

  Long getCompletedChapters();

  Long getPendingChapters();

  String getTeacherName();

  String getSubtopicName();

  String getSubtopicSlug();

  Long getTotalSubtopics();

  Long getCompletedSubtopics();

  Long getPendingSubtopics();

  List<String> getCompletedChapterNames();

  List<String> getCompletedChapterSlugs();

  List<String> getPendingChapterNames();

  List<String> getPendingChapterSlugs();
}
