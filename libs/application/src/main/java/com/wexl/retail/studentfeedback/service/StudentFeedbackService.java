package com.wexl.retail.studentfeedback.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.erp.attendance.repository.CalenderRepository;
import com.wexl.retail.messagetemplate.dto.MessageTemplateDto;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.studentfeedback.domain.StudentFeedback;
import com.wexl.retail.studentfeedback.dto.StudentFeedbackDto;
import com.wexl.retail.studentfeedback.dto.StudentFeedbackRequest;
import com.wexl.retail.studentfeedback.dto.StudentFeedbackResponse;
import com.wexl.retail.studentfeedback.model.FeedbackType;
import com.wexl.retail.studentfeedback.repository.StudentFeedbackRepository;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.util.Constants;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StudentFeedbackService {

  private final StudentFeedbackRepository studentFeedbackRepository;

  private final UserRepository userRepository;

  private final StudentRepository studentRepository;

  private final TaskInstRepository taskInstRepository;

  private final DateTimeUtil dateTimeUtil;

  private final CalenderRepository calenderRepository;
  private final MessageTemplateRepository messageTemplateRepository;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  public void createStudentFeedback(
      String studentAuthId, String orgSlug, StudentFeedbackRequest studentFeedbackRequest) {
    final CalenderDetails calenderDetailsFromEpoch =
        getCalenderDetailsFromEpoch(studentFeedbackRequest.getFeedbackDate());
    Student student = studentRepository.getStudentByAuthUserIdAndOrgSlug(studentAuthId, orgSlug);
    validateIfSubjectFeedbackExists(
        student,
        studentFeedbackRequest.getSubjectSlug(),
        calenderDetailsFromEpoch.getMonth(),
        studentFeedbackRequest.getType());
    validateIfSummaryFeedbackExists(
        student, calenderDetailsFromEpoch.getMonth(), studentFeedbackRequest.getType());
    StudentFeedback studentfeedback = new StudentFeedback();
    studentfeedback.setType(studentFeedbackRequest.getType());
    studentfeedback.setMessage(studentFeedbackRequest.getMessage());
    final LocalDateTime feedbackDate =
        dateTimeUtil.convertEpochToIso8601(studentFeedbackRequest.getFeedbackDate());
    studentfeedback.setFeedbackDate(feedbackDate);
    studentfeedback.setTaskInst(studentFeedbackRequest.getTaskInst());
    studentfeedback.setSubjectName(studentFeedbackRequest.getSubjectName());
    studentfeedback.setSubjectSlug(studentFeedbackRequest.getSubjectSlug());
    studentfeedback.setCalenderDetails(calenderDetailsFromEpoch);
    studentfeedback.setStudent(student);
    studentfeedback.setAcademicYearSlug(latestAcademicYear);
    if (studentFeedbackRequest.getType().equals(FeedbackType.SUMMARY)) {
      studentfeedback.setMessageTemplates(null);
    } else {
      var messageTemplate = retrieveMessageTemplate(studentFeedbackRequest.getFeedBackIds());
      studentfeedback.setMessageTemplates(messageTemplate);
    }
    studentFeedbackRepository.save(studentfeedback);
  }

  private MessageTemplateDto.Attributes retrieveMessageTemplate(List<Long> messageTemplateId) {
    List<MessageTemplate> messageTemplates =
        messageTemplateRepository.findAllById(messageTemplateId);

    if (messageTemplates.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.MessageTemplate");
    }
    return MessageTemplateDto.Attributes.builder().messageTemplate(messageTemplateId).build();
  }

  private void validateIfSubjectFeedbackExists(
      Student student, String subjectSlug, Integer monthId, FeedbackType feedbackType) {
    if (!FeedbackType.SUBJECT.equals(feedbackType)) {
      return;
    }
    var feedback =
        studentFeedbackRepository.findAllByStudentAndCalenderDetailsAndTypeAndSubjectSlug(
            student.getId(),
            FeedbackType.SUBJECT.toString(),
            subjectSlug,
            monthId,
            latestAcademicYear);
    if (!feedback.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.FeedBack.SubjectFeedbackAlreadyExist");
    }
  }

  private void validateIfSummaryFeedbackExists(
      Student student, Integer monthId, FeedbackType feedbackType) {
    if (!FeedbackType.SUMMARY.equals(feedbackType)) {
      return;
    }
    var feedback =
        studentFeedbackRepository.findAllByStudentAndCalenderDetailsAndTypeAndAcademicYearSlug(
            student.getId(), FeedbackType.SUMMARY.toString(), monthId, latestAcademicYear);

    if (!feedback.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.FeedBack.MonthlyFeedbackAlreadyExist");
    }
  }

  public CalenderDetails getCalenderDetailsFromEpoch(Long epochInMillis) {
    var calendarDetailFormat = dateTimeUtil.convertEpocToIntegerFormat(epochInMillis);
    final Optional<CalenderDetails> byDateId =
        calenderRepository.findByDateId(calendarDetailFormat);
    if (byDateId.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    return byDateId.get();
  }

  public List<StudentFeedbackResponse> getTeacherFeedbackResponse(
      String studentAuthId, Long fromDateInEpoch, Long toDateInEpoch) {

    var fromDate = dateTimeUtil.convertEpochToIso8601(fromDateInEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(toDateInEpoch);
    var user = userRepository.findByAuthUserId(studentAuthId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    Student student = user.get().getStudentInfo();
    List<String> status = new ArrayList<>();
    status.add(Constants.COMPLETED);
    status.add(Constants.PENDING);
    var taskInstList =
        taskInstRepository.getStudentActivityByDates(status, student.getId(), fromDate, toDate);
    List<StudentFeedbackResponse> data = new ArrayList<>();
    taskInstList.forEach(
        taskInst -> {
          var feedback =
              studentFeedbackRepository.findByStudentAndTaskInst(student, taskInst.getId());
          var task = taskInst.getTask();
          var exam = taskInst.getExam();
          data.add(
              StudentFeedbackResponse.builder()
                  .feedbackDate(
                      feedback
                          .map(
                              studentFeedback ->
                                  DateTimeUtil.convertIso8601ToEpoch(
                                      studentFeedback.getFeedbackDate()))
                          .orElse(null))
                  .type(feedback.map(StudentFeedback::getType).orElse(null))
                  .activityName(task.getName())
                  .id(feedback.isPresent() ? feedback.get().getId() : null)
                  .subjectName(task.getSubjectName())
                  .subjectSlug(task.getSubjectSlug())
                  .message(feedback.map(StudentFeedback::getMessage).orElse(null))
                  .taskInst(taskInst.getId())
                  .taskId(taskInst.getTask().getId())
                  .examId(exam != null ? exam.getId() : null)
                  .score(exam != null ? exam.getMarksScored() : null)
                  .synopsisSlug(task.getSynopsisSlug())
                  .chapterName(task.getChapterName())
                  .chapterSlug(task.getChapterSlug())
                  .subtopicName(task.getSubtopicName())
                  .subtopicSlug(task.getSubtopicSlug())
                  .videoSlug(task.getVideoSlug())
                  .activityType(task.getTaskType().name())
                  .totalMarks(exam != null ? exam.getTotalMarks() : null)
                  .isExamCorrected(exam != null && exam.isCorrected())
                  .feedBackMessage(
                      feedback.isEmpty()
                          ? Collections.emptyList()
                          : buildFeedBackMessage(feedback.get()))
                  .build());
        });

    final List<StudentFeedback> studentFeedbackByType =
        studentFeedbackRepository
            .findAllByStudentAndFeedbackDateBetweenAndTypeInAndAcademicYearSlug(
                student,
                fromDate,
                toDate,
                List.of(FeedbackType.SUBJECT, FeedbackType.SUMMARY),
                latestAcademicYear);
    data.addAll(studentFeedbackByType.stream().map(this::transformStudentFeedback).toList());
    return data;
  }

  public List<StudentFeedbackDto.Response> buildFeedBackMessage(StudentFeedback feedback) {
    List<StudentFeedbackDto.Response> messageList = new ArrayList<>();
    if (feedback.getMessageTemplates() == null) {
      return Collections.emptyList();
    }
    var messageTemplates =
        messageTemplateRepository.findAllById(feedback.getMessageTemplates().messageTemplate());
    messageTemplates.forEach(
        template ->
            messageList.add(
                StudentFeedbackDto.Response.builder()
                    .id(template.getId())
                    .message(template.getMessage())
                    .build()));
    return messageList;
  }

  public StudentFeedbackResponse transformStudentFeedback(StudentFeedback feedback) {
    return StudentFeedbackResponse.builder()
        .feedbackDate(DateTimeUtil.convertIso8601ToEpoch(feedback.getFeedbackDate()))
        .type(feedback.getType())
        .activityName(null)
        .id(feedback.getId())
        .subjectName(feedback.getSubjectName())
        .subjectSlug(feedback.getSubjectSlug())
        .message(feedback.getMessage())
        .feedBackMessage(buildFeedBackMessage(feedback))
        .taskInst(null)
        .examId(null)
        .score(null)
        .synopsisSlug(null)
        .videoSlug(null)
        .activityType(null)
        .build();
  }

  public void editFeedback(
      Long feedbackId, String studentAuthId, StudentFeedbackRequest studentFeedbackRequest) {

    User user = userRepository.getUserByAuthUserId(studentAuthId);
    Student student = user.getStudentInfo();
    var feedback = studentFeedbackRepository.findByStudentAndId(student, feedbackId);
    if (feedback.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid Feedback Id");
    }
    StudentFeedback studentFeedback = feedback.get();
    studentFeedback.setMessage(studentFeedbackRequest.getMessage());
    if (studentFeedbackRequest.getType().equals(FeedbackType.SUMMARY)) {
      studentFeedback.setMessageTemplates(null);
    } else {
      studentFeedback.setMessageTemplates(
          retrieveMessageTemplate(studentFeedbackRequest.getFeedBackIds()));
    }
    studentFeedbackRepository.save(studentFeedback);
  }
}
