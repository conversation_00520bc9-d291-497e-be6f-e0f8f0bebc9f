package com.wexl.retail.notifications.model;

import com.wexl.retail.classroom.core.model.Classroom;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "notification_classrooms")
public class ClassroomNotification {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "notification_id")
  private Notification notification;

  @ManyToOne
  @JoinColumn(name = "classroom_id")
  private Classroom classroom;

  @Column(name = "org_slug")
  private String orgSlug;
}
