package com.wexl.retail.lessonplanner.service;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.lessonplanner.dto.LessonPlannerDto;
import com.wexl.retail.lessonplanner.model.LessonPlanner;
import com.wexl.retail.lessonplanner.model.LessonPlannerDetails;
import com.wexl.retail.lessonplanner.model.TeacherTimeTableDetail;
import com.wexl.retail.lessonplanner.repository.*;
import com.wexl.retail.notifications.service.NotificationsService;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class LessonPlannerService {
  private final LessonPlannerRepository lessonPlannerRepository;
  private final LessonPlannerDetailRepository lessonPlannerDetailsRepository;
  private final TeacherTimeTableService teacherTimeTableService;
  private final LessonPlannerTemplateRepository lessonPlannerTemplateRepository;
  private final LessonPlannerTemplateFieldRepository lessonPlannerTemplateFieldRepository;
  private final NotificationsService notificationsService;
  private final ContentService contentService;
  private final TeacherTimeTableDetailsRepository teacherTimeTableDetailsRepository;
  private final DateTimeUtil dateTimeUtil;

  @Value("${app.contentToken}")
  private String contentBearerToken;

  @Transactional
  public void createLessonPlan(String orgSlug, LessonPlannerDto.LessonPlanRequest request) {

    TeacherTimeTableDetail teacherTimeTableDetail =
        teacherTimeTableService.validateTeacherTimeTableDetail(request.teacherTimeTableDetailId());
    List<String> attachments = request.attachment() == null ? null : List.of(request.attachment());
    LessonPlanner lessonPlanner =
        LessonPlanner.builder()
            .orgSlug(orgSlug)
            .chapterSlug(request.chapterSlug())
            .subtopicSlug(request.subtopicSlug())
            .lectureYoutubeUrl(request.youtubeUrl())
            .lectureVideo(request.lectureVideo())
            .attachments(attachments)
            .paragraph(request.paragraph())
            .teacherTimeTableDetail(teacherTimeTableDetail)
            .isPublished(request.isPublished())
            .build();

    List<LessonPlannerDetails> details =
        buildLessonPlannerDetails(lessonPlanner, request.fieldRequests());
    lessonPlanner.setLessonPlannerDetails(details);
    lessonPlannerRepository.save(lessonPlanner);
  }

  private List<LessonPlannerDetails> buildLessonPlannerDetails(
      LessonPlanner lessonPlanner, List<LessonPlannerDto.TemplateFieldRequest> fieldRequests) {
    List<LessonPlannerDetails> details = new ArrayList<>();
    fieldRequests.forEach(
        request -> {
          var lessonPlannerTemplateField =
              lessonPlannerTemplateFieldRepository.findById(request.fieldId()).orElseThrow();
          details.add(
              LessonPlannerDetails.builder()
                  .lessonPlanner(lessonPlanner)
                  .lessonPlannerTemplateField(lessonPlannerTemplateField)
                  .definition(request.definition())
                  .build());
        });
    return lessonPlannerDetailsRepository.saveAll(details);
  }

  public LessonPlannerDto.LessonPlannerResponse getLessonPlanById(Long lessonPlanId) {
    var lessonPlanner = validateLessonPlanner(lessonPlanId);
    var attachment =
        Objects.isNull(lessonPlanner.getAttachments())
            ? null
            : lessonPlanner.getAttachments().getFirst();
    var subtopicResponse =
        contentService.getSubTopicBySlug(
            lessonPlanner.getOrgSlug(), lessonPlanner.getSubtopicSlug(), contentBearerToken);
    return LessonPlannerDto.LessonPlannerResponse.builder()
        .id(lessonPlanner.getId())
        .chapterName(subtopicResponse.getChapterName())
        .subtopicName(subtopicResponse.getName())
        .chapterSlug(lessonPlanner.getChapterSlug())
        .subtopicSlug(lessonPlanner.getSubtopicSlug())
        .youtubeUrl(lessonPlanner.getLectureYoutubeUrl())
        .lectureVideoPath(lessonPlanner.getLectureVideo())
        .attachmentPath(attachment)
        .lectureVideo(
            Objects.isNull(lessonPlanner.getLectureVideo())
                ? null
                : notificationsService
                    .convertAttachmentToS3Link(
                        Collections.singletonList(lessonPlanner.getLectureVideo()))
                    .getFirst())
        .attachment(
            Objects.isNull(attachment)
                ? null
                : notificationsService
                    .convertAttachmentToS3Link(Collections.singletonList(attachment))
                    .getFirst())
        .paragraph(lessonPlanner.getParagraph())
        .templateId(
            lessonPlanner.getLessonPlannerDetails().isEmpty()
                ? null
                : lessonPlanner
                    .getLessonPlannerDetails()
                    .getFirst()
                    .getLessonPlannerTemplateField()
                    .getLessonPlannerTemplate()
                    .getId())
        .fieldResponses(buildFieldResponses(lessonPlanner.getLessonPlannerDetails()))
        .isPublished(lessonPlanner.getIsPublished())
        .build();
  }

  private List<LessonPlannerDto.TemplateFieldResponse> buildFieldResponses(
      List<LessonPlannerDetails> lessonPlannerDetails) {
    List<LessonPlannerDto.TemplateFieldResponse> responses = new ArrayList<>();
    if (lessonPlannerDetails.isEmpty()) {
      return responses;
    }
    lessonPlannerDetails.forEach(
        detail ->
            responses.add(
                LessonPlannerDto.TemplateFieldResponse.builder()
                    .lessonPlannerDetailId(detail.getId())
                    .fieldId(detail.getLessonPlannerTemplateField().getId())
                    .fieldName(detail.getLessonPlannerTemplateField().getFieldName())
                    .definition(detail.getDefinition())
                    .build()));
    return responses;
  }

  public void updateLessonPlan(Long lessonPlanId, LessonPlannerDto.LessonPlanRequest request) {
    var lessonPlanner = validateLessonPlanner(lessonPlanId);
    lessonPlanner.setChapterSlug(request.chapterSlug());
    lessonPlanner.setSubtopicSlug(request.subtopicSlug());
    lessonPlanner.setLectureYoutubeUrl(request.youtubeUrl());
    lessonPlanner.setLectureVideo(request.lectureVideo());
    lessonPlanner.setAttachments(
        request.attachment() == null ? null : List.of(request.attachment()));
    lessonPlanner.setParagraph(request.paragraph());
    if (request.fieldRequests().getFirst().lessonPlannerDetailId() == null) {
      lessonPlannerDetailsRepository.deleteAllByIdInBatch(
          lessonPlanner.getLessonPlannerDetails().stream()
              .map(LessonPlannerDetails::getId)
              .toList());
      buildLessonPlannerDetails(lessonPlanner, request.fieldRequests());
    } else {
      updateLessonPlannerDetails(request.fieldRequests());
    }
    lessonPlannerRepository.save(lessonPlanner);
  }

  private void updateLessonPlannerDetails(
      List<LessonPlannerDto.TemplateFieldRequest> templateFieldRequests) {
    List<LessonPlannerDetails> lessonPlannerDetails = new ArrayList<>();
    templateFieldRequests.forEach(
        request -> {
          var lessonPlannerDetail = validateLessonPlannerDetails(request.lessonPlannerDetailId());
          lessonPlannerDetail.setDefinition(request.definition());
          lessonPlannerDetails.add(lessonPlannerDetail);
        });
    lessonPlannerDetailsRepository.saveAll(lessonPlannerDetails);
  }

  @NotNull
  private LessonPlannerDetails validateLessonPlannerDetails(long id) {
    return lessonPlannerDetailsRepository.findById(id).orElseThrow();
  }

  @NotNull
  private LessonPlanner validateLessonPlanner(Long lessonPlanId) {
    return lessonPlannerRepository.findById(lessonPlanId).orElseThrow();
  }

  public List<LessonPlannerDto.LessonPlanTemplateResponse> getLessonPlanTemplates() {
    var lessonPlannerTemplates = lessonPlannerTemplateRepository.findAll();
    List<LessonPlannerDto.LessonPlanTemplateResponse> responses = new ArrayList<>();
    lessonPlannerTemplates.forEach(
        template ->
            responses.add(
                LessonPlannerDto.LessonPlanTemplateResponse.builder()
                    .id(template.getId())
                    .name(template.getName())
                    .fieldResponses(
                        template.getLessonPlannerTemplateFields().stream()
                            .map(
                                field ->
                                    LessonPlannerDto.TemplateFieldResponse.builder()
                                        .fieldId(field.getId())
                                        .fieldName(field.getFieldName())
                                        .definition(field.getDefinition())
                                        .build())
                            .toList())
                    .build()));
    return responses;
  }

  public void deleteLessonPlan(Long lessonPlanId) {
    lessonPlannerRepository.deleteById(lessonPlanId);
  }

  public void cloneLessonPlanner(
      String orgSlug, LessonPlannerDto.CloneLessonPlannerRequest request) {
    var originalLessonPlanner = validateLessonPlanner(request.lessonPlannerId());
    List<LocalDate> dates = new ArrayList<>();
    request
        .dates()
        .forEach(date -> dates.add(dateTimeUtil.convertEpochToIso8601(date).toLocalDate()));

    var targetTimeTableDetails =
        teacherTimeTableDetailsRepository.getByOrgAndSectionAndDate(
            orgSlug,
            dates,
            request.sections(),
            originalLessonPlanner.getTeacherTimeTableDetail().getTeacherSubject().getSubject());

    for (TeacherTimeTableDetail teacherTimeTableDetail : targetTimeTableDetails) {
      var lessonPlannerExist =
          lessonPlannerRepository.findByTeacherTimeTableDetail(teacherTimeTableDetail);
      if (lessonPlannerExist.isEmpty()) {
        LessonPlanner clonedPlanner =
            LessonPlanner.builder()
                .orgSlug(originalLessonPlanner.getOrgSlug())
                .chapterSlug(originalLessonPlanner.getChapterSlug())
                .subtopicSlug(originalLessonPlanner.getSubtopicSlug())
                .lectureYoutubeUrl(originalLessonPlanner.getLectureYoutubeUrl())
                .lectureVideo(originalLessonPlanner.getLectureVideo())
                .attachments(originalLessonPlanner.getAttachments())
                .paragraph(originalLessonPlanner.getParagraph())
                .teacherTimeTableDetail(teacherTimeTableDetail)
                .build();

        List<LessonPlannerDetails> clonedDetails = new ArrayList<>();
        for (LessonPlannerDetails detail : originalLessonPlanner.getLessonPlannerDetails()) {
          LessonPlannerDetails clonedDetail =
              LessonPlannerDetails.builder()
                  .lessonPlanner(clonedPlanner)
                  .lessonPlannerTemplateField(detail.getLessonPlannerTemplateField())
                  .definition(detail.getDefinition())
                  .build();
          clonedDetails.add(clonedDetail);
        }
        clonedPlanner.setLessonPlannerDetails(clonedDetails);
        lessonPlannerRepository.save(clonedPlanner);
      }
    }
  }

  public void publishLessonPlanner(
      Long lessonPlanId, LessonPlannerDto.LessonPlannerPublishRequest request) {
    LessonPlanner lessonPlanner = validateLessonPlanner(lessonPlanId);
    lessonPlanner.setIsPublished(request.publishStatus());
    lessonPlannerRepository.save(lessonPlanner);
  }
}
