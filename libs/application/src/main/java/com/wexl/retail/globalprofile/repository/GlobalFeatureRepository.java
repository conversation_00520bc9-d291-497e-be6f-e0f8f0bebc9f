package com.wexl.retail.globalprofile.repository;

import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.GlobalFeature;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface GlobalFeatureRepository extends JpaRepository<GlobalFeature, Long> {
  Optional<GlobalFeature> findBySlug(String slug);

  List<GlobalFeature> findBySlugAndTemplate(String slug, AppTemplate template);

  List<GlobalFeature> findAllByTemplateAndDeletedAtIsNull(AppTemplate roleTemplate);

  List<GlobalFeature> findAllByIdIn(List<Long> globalFeatureIds);
}
