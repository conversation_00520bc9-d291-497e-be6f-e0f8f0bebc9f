package com.wexl.retail.globalprofile.repository;

import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RoleTemplateRepository extends JpaRepository<RoleTemplate, Long> {
  List<RoleTemplate> findBySlug(String slug);

  List<RoleTemplate> findAllByGlobalProfile(GlobalProfile globalProfile);

  List<RoleTemplate> findByGlobalProfileAndTemplate(
      GlobalProfile globalProfile, AppTemplate template);

  @Query(
      value =
          "SELECT * FROM role_templates rt WHERE rt.global_profile_id = :globalProfileId AND rt.template = :template AND rt.name NOT LIKE 'Manager'",
      nativeQuery = true)
  List<RoleTemplate> getTeacherRoleTemplateByGlobalProfileAndTemplate(
      @Param("globalProfileId") Long globalProfileId, @Param("template") String template);
}
