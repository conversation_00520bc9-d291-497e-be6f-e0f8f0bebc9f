package com.wexl.retail.notifications.resolver;

import com.wexl.retail.notifications.dto.EmailRecipientGroup;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.repository.TeacherRepository;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminRecipientResolver implements RecipientResolver {

  @Autowired private TeacherRepository teacherRepository;

  @Override
  public boolean supports(NotificationDto.SendTo sendTo) {
    return sendTo.groups() != null && sendTo.groups().contains(EmailRecipientGroup.ADMIN.name());
  }

  @Override
  public List<Object> resolveRecipients(String orgSlug, NotificationDto.SendTo sendTo) {
    return new ArrayList<>(teacherRepository.getAllAdminsByOrg(orgSlug));
  }
}
