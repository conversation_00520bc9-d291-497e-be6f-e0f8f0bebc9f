package com.wexl.retail.syllabustracking.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "syllabus_trackings")
public class SyllabusTracking extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String subtopicSlug;

  private String subtopicName;

  private String chapterSlug;

  private String chapterName;

  @Enumerated(EnumType.STRING)
  private SyllabusTrackingStatus status;

  private LocalDateTime dueDate;

  private String orgSlug;

  private String sectionUuid;

  private String sectionName;

  private String gradeSlug;

  private String gradeName;

  private String boardSlug;

  private String subjectSlug;

  private String subjectName;

  private String academicYearSlug;

  private Long updatedBy;
}
