package com.wexl.retail.lessonplanner.controller;

import com.wexl.retail.lessonplanner.dto.LessonPlannerDto;
import com.wexl.retail.lessonplanner.service.LessonPlannerService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/lesson-planners")
@RequiredArgsConstructor
public class LessonPlannerController {
  private final LessonPlannerService lessonPlannerService;

  @PostMapping
  public void createLessonPlan(
      @PathVariable String orgSlug, @RequestBody LessonPlannerDto.LessonPlanRequest request) {
    lessonPlannerService.createLessonPlan(orgSlug, request);
  }

  @GetMapping("/{lessonPlanId}")
  public LessonPlannerDto.LessonPlannerResponse getLessonPlan(@PathVariable Long lessonPlanId) {
    return lessonPlannerService.getLessonPlanById(lessonPlanId);
  }

  @PutMapping("/{lessonPlanId}")
  public void updateLessonPlan(
      @PathVariable Long lessonPlanId, @RequestBody LessonPlannerDto.LessonPlanRequest request) {
    lessonPlannerService.updateLessonPlan(lessonPlanId, request);
  }

  @GetMapping("/templates")
  public List<LessonPlannerDto.LessonPlanTemplateResponse> getLessonPlanTemplate() {
    return lessonPlannerService.getLessonPlanTemplates();
  }

  @DeleteMapping("/{lessonPlanId}")
  public void deleteLessonPlan(@PathVariable Long lessonPlanId) {
    lessonPlannerService.deleteLessonPlan(lessonPlanId);
  }

  @PostMapping("/lesson-planner:clone")
  public void cloneLessonPlanner(
      @PathVariable String orgSlug,
      @RequestBody LessonPlannerDto.CloneLessonPlannerRequest request) {
    lessonPlannerService.cloneLessonPlanner(orgSlug, request);
  }

  @PostMapping("/{lessonPlanId}:publish")
  public void publishLessonPlanner(
      @PathVariable Long lessonPlanId,
      @RequestBody LessonPlannerDto.LessonPlannerPublishRequest request) {
    lessonPlannerService.publishLessonPlanner(lessonPlanId, request);
  }
}
