package com.wexl.retail.studentfeedback.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.messagetemplate.dto.MessageType;
import com.wexl.retail.model.Student;
import com.wexl.retail.studentfeedback.model.FeedbackType;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class StudentFeedbackRequest {

  @NotNull private FeedbackType type;

  @Length(max = 3000, message = "The field must be less than 3000 characters")
  String message;

  @JsonProperty("subject_name")
  private String subjectName;

  @JsonProperty("subject_slug")
  private String subjectSlug;

  @JsonProperty("feedback_date")
  @NotNull
  private Long feedbackDate;

  @JsonProperty("task_inst_id")
  private Long taskInst;

  private Student student;

  @JsonProperty("feedback_type")
  private MessageType feedBackType;

  @JsonProperty("feedback_id")
  private List<Long> feedBackIds;
}
