package com.wexl.retail.studentfeedback.repository;

import com.wexl.retail.feedback.repository.TeacherFeedBackCounts;
import com.wexl.retail.feedback.repository.TeacherFeedbackCountDetails;
import com.wexl.retail.model.Student;
import com.wexl.retail.studentfeedback.domain.StudentFeedback;
import com.wexl.retail.studentfeedback.model.FeedbackType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentFeedbackRepository extends JpaRepository<StudentFeedback, Long> {

  List<StudentFeedback> findAllByStudentAndFeedbackDateBetweenAndTypeInAndAcademicYearSlug(
      Student studentId,
      LocalDateTime fromDateInEpoch,
      LocalDateTime toDateInEpoch,
      List<FeedbackType> types,
      String academicYearSlug);

  @Query(
      value =
          """
                select sf.* from classroom_student_feedback sf
                  inner join calender_details cd on sf.calender_details_id = cd.id
                 where sf.student_id = :studentId and sf.type = :type and sf.subject_slug =:subjectSlug
                 and cd.month = :monthId AND (cast((:academicYearSlug) as varChar) is null or sf.academic_year_slug in (:academicYearSlug))

              """,
      nativeQuery = true)
  List<StudentFeedback> findAllByStudentAndCalenderDetailsAndTypeAndSubjectSlug(
      Long studentId, String type, String subjectSlug, Integer monthId, String academicYearSlug);

  @Query(
      value =
          """
                        select sf.* from classroom_student_feedback sf
                         inner join calender_details cd on sf.calender_details_id = cd.id
                         where sf.student_id = :studentId and sf.type = :type
                         and cd.month = :monthId and sf.academic_year_slug = :academicYearSlug

                      """,
      nativeQuery = true)
  List<StudentFeedback> findAllByStudentAndCalenderDetailsAndTypeAndAcademicYearSlug(
      Long studentId, String type, Integer monthId, String academicYearSlug);

  Optional<StudentFeedback> findByStudentAndTaskInst(Student studentId, Long taskInstId);

  Optional<StudentFeedback> findByStudentAndId(Student studentId, Long feedbackId);

  @Query(
      value =
          """
                        select sf.* from classroom_student_feedback sf
                          inner join calender_details cd on sf.calender_details_id = cd.id
                         where sf.student_id = :studentId and sf.type = :type
                         and cd.month = :monthId order by created_at desc limit 1

                      """,
      nativeQuery = true)
  StudentFeedback getOverAllFeedBack(Long studentId, String type, Integer monthId);

  @Query(
      value =
          """
                           select teacher_id as teacherId,count(t.id) as taskCount ,
                           case when csf.message_templates is not null then 'TRUE'
                             when csf.message_templates is null then 'FALSE' end feedBack
                           from classrooms c
                           join classroom_teachers ct on c.id = ct.classroom_id
                           join classroom_schedules cs on cs.classroom_id  = c.id
                           join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                           join tasks t on t.classroom_schedule_inst_id = csi.id
                           join teacher_details td on td.id = ct.teacher_id
                           join users u on u.id = td.user_id
                           join task_inst ti on ti.task_id = t.id
                           left join classroom_student_feedback csf on csf.task_inst = ti.id
                           where csi.start_time between :fromDate and :toDate and c.deleted_at is null
                           and ti.completion_status in ('COMPLETED')
                           and task_type in (:taskType)
                           group by teacher_id,u.first_name, u.last_name,feedBack
                           order by teacher_id desc

                          """,
      nativeQuery = true)
  List<TeacherFeedBackCounts> getFeedbackCountsByTeacher(
      LocalDateTime fromDate, LocalDateTime toDate, String taskType);

  @Query(
      value =
          """
                  select teacher_id as teacherId,
                  COALESCE(sum(case when csf.message is not null then 1 end),0) as OverAllFeedBackGivenCount ,
                  COALESCE(sum(case when csf.message is null then 1 end),0)  as  OverAllFeedBackNotGivenCount from classrooms c
                  join classroom_teachers ct on c.id = ct.classroom_id
                  join classroom_students cs on cs.classroom_id = c.id
                  left join classroom_student_feedback csf on csf.student_id = cs.student_id
                  and csf.feedback_date between :fromDate and :toDate
                  and type in ('SUMMARY') group by teacher_id order by teacher_id desc

                       """,
      nativeQuery = true)
  List<TeacherFeedBackCounts> getOverallFeedbackCounts(
      LocalDateTime fromDate, LocalDateTime toDate);

  @Query(
      value =
          """
                  select csf.id as Id,teacher_id as teacherId,ti.id as taskInstId,c.name as classroomName,t.chapter_name as chapterName,t.chapter_slug as chapterSlug,
                  t.subject_name as subjectName,t.subject_slug as subjectSlug,t.subtopic_slug as subtopicSlug,t.subtopic_name as subtopicName,
                  case when csf.message_templates is not null then 'TRUE'
                        when csf.message_templates is null then 'FALSE' end feedBack,
                  s.id as studentId,t.name as taskName,t.id as taskId
                  from classrooms c
                   join classroom_teachers ct on c.id = ct.classroom_id
                   join classroom_schedules cs on cs.classroom_id  = c.id
                   join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                   join tasks t on t.classroom_schedule_inst_id = csi.id
                   join teacher_details td on td.id = ct.teacher_id
                   join users u on u.id = td.user_id
                   join task_inst ti on ti.task_id = t.id
                   join students s on s.id = ti.student_id
                   join users us on us.id  = s.user_id
                   left join classroom_student_feedback csf on csf.task_inst = ti.id
                   where csi.start_time between :fromDate and :toDate and c.deleted_at is null
                   and ti.completion_status in ('COMPLETED') and task_type in (:taskType)
                   and teacher_id = :teacherId and t.org_slug =:orgSlug
                   group by teacher_id,u.first_name, u.last_name,feedBack,ti.id,classroomName,chapterName,chapterSlug,
                   subjectName,subjectSlug,subtopicSlug,subtopicName,taskId,studentId,taskName,csf.id
                    order by teacher_id desc

                         """,
      nativeQuery = true)
  List<TeacherFeedbackCountDetails> getFeedbackDetails(
      String orgSlug,
      Long teacherId,
      LocalDateTime fromDate,
      LocalDateTime toDate,
      String taskType);

  @Query(
      value =
          """
              select  teacherId,feedback_id , studentId, message ,string_agg(classroomName, ', ') as classroomName from
              (select csf.id as feedback_id,teacher_id as teacherId,c.name as classroomName ,
               s.id as studentId,csf.message as message
              from classroom_student_feedback csf
              join students s on s.id  = csf.student_id
              join classroom_students cs on s.id = cs.student_id and cs.student_id  = csf.student_id
              join classrooms c on c.id = cs.classroom_id
              join classroom_teachers ct on ct.classroom_id = c.id
              where "type" = :feedbackType   and  csf.feedback_date between :fromDate and :toDate
              and c.org_slug = :orgSlug and ct.teacher_id = :teacherId
              ) a  group by teacherId, studentId, message,feedback_id""",
      nativeQuery = true)
  List<TeacherFeedbackCountDetails> getOverallFeedbackDetails(
      String orgSlug,
      long teacherId,
      String feedbackType,
      LocalDateTime fromDate,
      LocalDateTime toDate);
}
