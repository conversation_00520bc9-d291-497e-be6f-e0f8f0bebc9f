package com.wexl.retail.liveworksheet.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.content.model.Question;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto;
import com.wexl.retail.liveworksheet.service.LiveWorkSheetService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("orgs/{orgSlug}/live-worksheets")
public class LiveWorksheetController {

  private final LiveWorkSheetService liveWorkSheetService;

  @PostMapping("/image-question:upload")
  public LiveWorkSheetDto.ImageQuestionResponse uploadImageQuestion(
      @PathVariable String orgSlug, @RequestPart(value = "files") MultipartFile imageQuestion) {
    return liveWorkSheetService.uploadImageQuestion(orgSlug, imageQuestion);
  }

  @GetMapping("/{id}")
  public List<Question> getWorksheetResult(
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @PathVariable String orgSlug,
      @PathVariable long id) {
    return liveWorkSheetService.getLiveWorksheetResult(orgSlug, id, bearerToken);
  }
}
