package com.wexl.retail.studentfeedback.domain;

import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.messagetemplate.dto.MessageTemplateDto;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.studentfeedback.model.FeedbackType;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@Table(name = "classroom_student_feedback")
@AllArgsConstructor
public class StudentFeedback extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private Long taskInst;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @Enumerated(EnumType.STRING)
  private FeedbackType type;

  @Column(name = "message", columnDefinition = "VARCHAR(3000)")
  private String message;

  @Column(name = "feedback_date")
  private LocalDateTime feedbackDate;

  private String subjectName;
  private String subjectSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "calender_details_id")
  private CalenderDetails calenderDetails;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "feedback_id")
  private MessageTemplate messageTemplate;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private MessageTemplateDto.Attributes messageTemplates;

  @JoinColumn(name = "academic_year_slug")
  private String academicYearSlug;
}
