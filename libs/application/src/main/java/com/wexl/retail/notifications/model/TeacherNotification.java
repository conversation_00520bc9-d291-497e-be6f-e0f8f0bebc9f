package com.wexl.retail.notifications.model;

import com.wexl.retail.model.Teacher;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "notification_teachers")
public class TeacherNotification {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "notification_id")
  private Notification notification;

  @ManyToOne
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;

  @Column(name = "org_slug")
  private String orgSlug;
}
