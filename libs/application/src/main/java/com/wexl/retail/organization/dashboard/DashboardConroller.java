package com.wexl.retail.organization.dashboard;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.organization.dto.ServicesCount;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DashboardConroller {

  private final DashboardService dashboardService;

  @GetMapping("/orgs/{orgSlug}/teachers/{authUserId}/user-stats")
  public ChildOrgs getChildInfo(@PathVariable String authUserId) {
    return dashboardService.getMyOrgsInfo(authUserId);
  }

  @GetMapping("/orgs/{orgSlug}/child-orgs/{childOrgSlug}/activity")
  public ServicesCount getTotalServicesUsed(
      @PathVariable("childOrgSlug") String orgSlug,
      @RequestParam("from_date") long fromDate,
      @RequestParam("to_date") long toDate) {
    try {
      return dashboardService.getServicesUsage(orgSlug, fromDate, toDate);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Organization.Services", e);
    }
  }
}
