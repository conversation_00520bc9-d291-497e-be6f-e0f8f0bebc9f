package com.wexl.retail.organization.repository;

import com.fasterxml.jackson.annotation.JsonProperty;

public interface MlpAnalysisByInstituteData {
  @JsonProperty("nameOrgs")
  String getNameOrgs();

  @JsonProperty("usersCount")
  Long getUsersCount();

  @JsonProperty("teachersCount")
  Long getTeachersCount();

  @JsonProperty("mlpsAttendence")
  Long getMlpsAttendence();

  @JsonProperty("mlpsTriggered")
  Long getMlpsTriggered();
}
