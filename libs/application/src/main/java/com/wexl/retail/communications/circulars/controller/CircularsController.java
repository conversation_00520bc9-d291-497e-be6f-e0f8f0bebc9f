package com.wexl.retail.communications.circulars.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.communications.circulars.dto.CircularsDto;
import com.wexl.retail.communications.circulars.service.CircularsService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class CircularsController {

  private final CircularsService circularsService;

  @IsOrgAdminOrTeacher
  @PostMapping("/teachers/{teacherAuthUserId}/circulars")
  @ResponseStatus(HttpStatus.CREATED)
  public void createCircularNotification(
      @RequestBody CircularsDto.CircularRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {

    circularsService.createCircularNotification(orgSlug, request, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/teachers/{teacherAuthId}/circulars/{notificationId}")
  public void updateCircularNotification(
      @PathVariable String orgSlug,
      @PathVariable("notificationId") Long notificationId,
      @RequestBody CircularsDto.CircularRequest request) {
    circularsService.updateCircularNotification(orgSlug, notificationId, request);
  }

  @GetMapping("/circulars")
  public CircularsDto.CircularResponse getCircularNotification(@PathVariable String orgSlug) {
    return circularsService.getCircularNotifications(orgSlug);
  }
}
