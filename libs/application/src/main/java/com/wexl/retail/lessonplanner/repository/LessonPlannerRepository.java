package com.wexl.retail.lessonplanner.repository;

import com.wexl.retail.lessonplanner.model.LessonPlanner;
import com.wexl.retail.lessonplanner.model.TeacherTimeTableDetail;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LessonPlannerRepository extends JpaRepository<LessonPlanner, Long> {
  Optional<LessonPlanner> findByTeacherTimeTableDetail(TeacherTimeTableDetail detail);
}
