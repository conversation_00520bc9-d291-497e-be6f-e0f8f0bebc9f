package com.wexl.retail.relationship;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "student_relationship", schema = "public")
public class StudentRelationshipModel extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne
  @JoinColumn(name = "student_a_id")
  private Student studentA;

  @OneToOne
  @JoinColumn(name = "student_b_id")
  private Student studentB;

  @OneToOne
  @JoinColumn(name = "relationship_model_id")
  private RelationshipTypeModel relationshipModel;
}
