package com.wexl.retail.notifications.model;

import com.wexl.retail.notifications.dto.Message;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "scheduled_messages")
public class ScheduledMessage {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "recipient_name")
  private String recipientName;

  @Column(name = "mobile_number")
  private String mobileNumber;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "section_name")
  private String sectionName;

  @Column(name = "teacher_name")
  private String teacherName;

  @Column(name = "date")
  private String date;

  @Column(name = "scheduled_time")
  private LocalDateTime scheduledTime;

  @Column(name = "sent")
  private boolean sent;

  @Column(name = "message_type")
  private List<Message> messageType;

  @Column(name = "attachment")
  private String attachment;

  @Column(name = "sms_dlt_template_id")
  private String smsDltTemplateId;

  @Column(name = "whatsapp_template_id")
  private String whatsAppTemplateId;

  @Column(name = "is_fee_due")
  private boolean isFeeDueMessage;
}
