package com.wexl.retail.calenderevent.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "calender_event_users")
public class CalendarEventUser extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "user_id")
  private Long userId;

  private Boolean isStudent;

  private Boolean isTeacher;

  @ManyToOne
  @JoinColumn(name = "calendar_event_id")
  private CalendarEvent calendarEvent;
}
