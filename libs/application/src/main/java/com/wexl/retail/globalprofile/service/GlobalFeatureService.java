package com.wexl.retail.globalprofile.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalFeatureRequest;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalFeatureResponse;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.GlobalFeature;
import com.wexl.retail.globalprofile.repository.GlobalFeatureRepository;
import com.wexl.retail.globalprofile.repository.GlobalProfileRepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class GlobalFeatureService {

  private final GlobalFeatureRepository globalFeatureRepository;
  private final GlobalProfileRepository globalProfileRepository;

  public List<GlobalFeatureResponse> getAllFeatures(AppTemplate role) {
    if (role != null) {
      var globalFeatures =
          globalFeatureRepository.findAllByTemplateAndDeletedAtIsNull(
              AppTemplate.valueOf(role.name()));
      return globalFeatures.stream().map(this::buildGlobalFetureResponse).toList();
    }
    final List<GlobalFeature> globalFeatures = globalFeatureRepository.findAll();
    return globalFeatures.stream()
        .filter(p -> p.getTemplate() != null)
        .map(this::buildGlobalFetureResponse)
        .toList();
  }

  public GlobalFeatureResponse buildGlobalFetureResponse(GlobalFeature p) {
    return GlobalFeatureResponse.builder()
        .id(p.getId())
        .name(p.getName())
        .description(p.getDescription())
        .slug(p.getSlug())
        .roleTemplate(p.getTemplate().name())
        .build();
  }

  public void saveFeature(GlobalFeatureRequest globalProfileRequest) {
    List<GlobalFeature> globalFeature =
        globalFeatureRepository.findBySlugAndTemplate(
            globalProfileRequest.slug(), globalProfileRequest.template());
    if (!globalFeature.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.GlobalFeature.exists");
    }
    globalFeatureRepository.save(
        GlobalFeature.builder()
            .name(globalProfileRequest.name())
            .description(globalProfileRequest.description())
            .slug(globalProfileRequest.slug())
            .template(globalProfileRequest.template())
            .build());
  }

  public void updateFeature(Long id, GlobalFeatureRequest globalProfileRequest) {
    final Optional<GlobalFeature> possibleGlobalFeature = globalFeatureRepository.findById(id);
    if (possibleGlobalFeature.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }

    final GlobalFeature globalFeature = possibleGlobalFeature.get();
    globalFeature.setName(globalProfileRequest.name());
    globalFeature.setDescription(globalProfileRequest.description());
    globalFeature.setSlug(globalProfileRequest.slug());
    globalFeature.setTemplate(globalProfileRequest.template());
    globalFeatureRepository.save(globalFeature);
  }

  //  public void deleteGlobalFeature(Long id) {
  //    final Optional<GlobalFeature> possibleGlobalFeature = globalFeatureRepository.findById(id);
  //    if (possibleGlobalFeature.isEmpty()) {
  //      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
  //    }
  //    final GlobalFeature globalFeature = possibleGlobalFeature.get();
  //    final List<GlobalProfile> globalProfiles =
  //        globalProfileRepository.findByFeaturesIn(List.of(globalFeature));
  //    if (!globalProfiles.isEmpty()) {
  //      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
  //    }
  //
  //    globalFeatureRepository.deleteById(id);
  //  }

  public GlobalFeatureResponse getFeature(Long id) {
    final Optional<GlobalFeature> possibleGlobalFeature = globalFeatureRepository.findById(id);
    if (possibleGlobalFeature.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    final GlobalFeature globalFeature = possibleGlobalFeature.get();
    return GlobalFeatureResponse.builder()
        .id(globalFeature.getId())
        .name(globalFeature.getName())
        .description(globalFeature.getDescription())
        .slug(globalFeature.getSlug())
        .roleTemplate(globalFeature.getTemplate().name())
        .build();
  }
}
