package com.wexl.retail.notifications.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.service.EmailNotificationService;
import com.wexl.retail.organization.admin.StudentResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherAuthUserId}/email-notifications")
public class EmailNotificationController {
  private final EmailNotificationService emailNotificationService;

  @IsOrgAdminOrTeacher
  @PostMapping()
  @ResponseStatus(HttpStatus.CREATED)
  public void sendEmail(
      @RequestBody NotificationDto.EmailRequest emailRequest,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {
    emailNotificationService.createEmailNotification(orgSlug, emailRequest, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/birthdays")
  @ResponseStatus(HttpStatus.CREATED)
  public void sendBirthdayEmail(
      @RequestBody NotificationDto.EmailRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {
    emailNotificationService.sendBirthdayEmailNotification(request, orgSlug, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/students-fee-due")
  @ResponseStatus(HttpStatus.CREATED)
  public void sendFeeDueEmail(
      @RequestBody NotificationDto.EmailRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {
    emailNotificationService.sendFeeDueEmailNotification(request, orgSlug, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/fee-dues")
  public List<StudentResponse> getFeeDueStudents(@PathVariable String orgSlug) {
    return emailNotificationService.getFeeDueStudents(orgSlug);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/birthdays")
  public List<StudentResponse> getBirthdayStudents(@PathVariable String orgSlug) {
    return emailNotificationService.getBirthdayStudents(orgSlug);
  }

  @IsOrgAdminOrTeacher
  @GetMapping()
  public NotificationDto.EmailNotifications getEmailNotifications(@PathVariable String orgSlug) {
    return emailNotificationService.getEmailNotifications(orgSlug);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/messages")
  @ResponseStatus(HttpStatus.CREATED)
  public void sendMessages(
      @RequestBody NotificationDto.EmailRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {
    emailNotificationService.sendMessages(request, orgSlug, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/messages")
  public NotificationDto.EmailNotifications getMessageNotifications(@PathVariable String orgSlug) {
    return emailNotificationService.getMessageNotifications(orgSlug);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/fee-dues")
  @ResponseStatus(HttpStatus.CREATED)
  public void sendFeeDueMessages(
      @RequestBody NotificationDto.EmailRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId) {
    emailNotificationService.sendFeeDueMessageNotification(request, orgSlug, teacherAuthId);
  }
}
