package com.wexl.retail.syllabustracking.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.syllabustracking.model.SyllabusTrackingStatus;
import java.util.List;
import lombok.Builder;

public record SyllabusDto() {

  public record SyllabusStatusRequest(
      SyllabusTrackingStatus status, @JsonProperty("subtopic_slugs") String subtopics) {}

  @Builder
  public record SyllabusTrackingResponse(
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      List<ChapterResponse> chapters) {}

  @Builder
  public record ChapterResponse(
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      List<SubtopicResponse> subtopics) {}

  @Builder
  public record SubtopicResponse(
      @JsonProperty("syllabus_tracking_id") long id,
      @JsonProperty("subtopic_name") String subtopicName,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      SyllabusTrackingStatus status,
      Long completedDate) {}

  @Builder
  public record ChapterCounts(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("total_chapters_count") Long totalChapters,
      @JsonProperty("completed_chapters_count") Long completedChapters,
      @JsonProperty("completed_chapters") List<Chapters> completedChaptersList,
      @JsonProperty("pending_chapters_count") Long pendingChapters,
      @JsonProperty("pending_chapters") List<Chapters> pendingChaptersList) {}

  @Builder
  public record Chapters(
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug) {}

  @Builder
  public record subTopicDetails(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("teacher_name") String teacherName,
      @JsonProperty("total_subtopic_count") Long totalSubTopics,
      @JsonProperty("completed_subtopic_count") Long completedSubTopicsCount,
      @JsonProperty("pending_subtopic_count") Long pendingSubTopicsCount,
      @JsonProperty("completed_subtopics") List<subTopics> completedSubTopics,
      @JsonProperty("pending_subtopics") List<subTopics> pendingSubTopics) {}

  @Builder
  public record subTopics(
      @JsonProperty("subtopic_name") String subTopicName,
      @JsonProperty("subtopic_slug") String subTopicSlug,
      @JsonProperty("subject_name") String subjectName) {}
}
