package com.wexl.retail.messagetemplate.category.dto;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
public enum CategoryType {
  NOTIFICATION("NOTIFICATION"),
  FEEDBACK("FEEDBACK"),
  EMAIL("EMAIL"),
  SMS_WHATSAPP("SMS_WHATSAPP");

  private final String value;

  public static CategoryType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (CategoryType enumEntry : CategoryType.values()) {
      if (enumEntry.toString().equals(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Cannot create enum from " + value + " value!");
  }
}
