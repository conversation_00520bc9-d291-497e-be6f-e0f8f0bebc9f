package com.wexl.retail.lessonplanner.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.section.domain.TeacherSubjects;
import jakarta.persistence.*;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "teacher_time_table_details")
public class TeacherTimeTableDetail extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_subject_id")
  private TeacherSubjects teacherSubject;

  @Column(name = "start_time")
  private LocalDateTime startTime;

  @Column(name = "end_time")
  private LocalDateTime endTime;

  @Column(name = "room_number")
  private String roomNumber;

  @Column(name = "date")
  private LocalDate date;

  @Enumerated(EnumType.STRING)
  @Column(name = "day_of_week")
  private DayOfWeek dayOfWeek;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_time_table_id")
  private TeacherTimeTable teacherTimeTable;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "period_table_id")
  private PeriodTable periodTable;
}
