package com.wexl.retail.organization.ambassador.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record DiscountDto() {
  @Builder
  public record DiscountResponse(
      @JsonProperty("id") Long id,
      @JsonProperty("product_id") Long productId,
      @JsonProperty("discount_type") DiscountType discountType,
      @JsonProperty("discount_code") String discountCode,
      @JsonProperty("discount_value") String discountValue) {}
}
