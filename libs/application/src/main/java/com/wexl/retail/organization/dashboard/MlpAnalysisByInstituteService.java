package com.wexl.retail.organization.dashboard;

import com.wexl.retail.organization.repository.OrganizationRepository;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MlpAnalysisByInstituteService {
  @Autowired private OrganizationRepository organizationRepository;

  public List<MlpAnalysisByInstituteResponse> getMlpAnalysisByInstitute(int timePeriod, int limit) {
    LocalDate toDate = LocalDate.now();
    LocalDate fromDate = LocalDate.now().minusMonths(timePeriod);
    return organizationRepository.getMlpAnalysisByInstituteData(fromDate, toDate, limit).stream()
        .map(
            mlpAnalysisByInstitute ->
                MlpAnalysisByInstituteResponse.builder()
                    .nameOrgs(mlpAnalysisByInstitute.getNameOrgs())
                    .usersCount(mlpAnalysisByInstitute.getUsersCount())
                    .teachersCount(mlpAnalysisByInstitute.getTeachersCount())
                    .mlpsAttendence(mlpAnalysisByInstitute.getMlpsAttendence())
                    .mlpsTriggered(mlpAnalysisByInstitute.getMlpsTriggered())
                    .build())
        .toList();
  }
}
