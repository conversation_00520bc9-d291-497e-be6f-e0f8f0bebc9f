package com.wexl.retail.globalprofile.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.RoleTemplateResponse;
import com.wexl.retail.globalprofile.service.RoleTemplateService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@IsOrgAdmin
@RequiredArgsConstructor
@RestController
public class RoleTemplateController {
  private final RoleTemplateService roleTemplateService;

  @GetMapping("/orgs/{orgSlug}/global-profiles/{profileId}/role-templates")
  public List<RoleTemplateResponse> getAllRoleTemplates(@PathVariable Long profileId) {
    return roleTemplateService.getAllRoleTemplatesForProfile(profileId);
  }

  @PostMapping("/orgs/wexl-internal/global-profiles/{profileId}/role-templates")
  public void createRoleTemplate(
      @Valid @RequestBody GlobalProfileDto.RoleTemplateRequest roleTemplateRequest,
      @PathVariable Long profileId) {
    roleTemplateService.saveRoleTemplate(roleTemplateRequest, profileId);
  }

  @GetMapping("/orgs/wexl-internal/global-profiles/{profileId}/role-templates/{roleTemplateId}")
  public RoleTemplateResponse getRoleTemplate(@PathVariable Long roleTemplateId) {
    return roleTemplateService.getRoleTemplate(roleTemplateId);
  }

  @DeleteMapping("/orgs/wexl-internal/global-profiles/{profileId}/role-templates/{roleTemplateId}")
  public void deleteById(@PathVariable Long roleTemplateId) {
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DeleteRoleTemplate");
  }

  @PutMapping("/orgs/wexl-internal/global-profiles/{profileId}/role-templates/{roleTemplateId}")
  public void updateRoleTemplate(
      @Valid @RequestBody GlobalProfileDto.RoleTemplateRequest roleTemplateRequest,
      @PathVariable Long roleTemplateId) {
    roleTemplateService.updateRoleTemplate(roleTemplateRequest, roleTemplateId);
  }
}
