package com.wexl.retail.organization.dashboard;

import com.wexl.retail.classroom.core.service.ScheduleInstAttendanceService;
import com.wexl.retail.commons.caching.CacheConstants;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsManagerOrOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.elp.dto.SignupDto;
import com.wexl.retail.elp.service.StudentSignupService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.organization.auth.OrganizationAuthService;
import com.wexl.retail.organization.auth.OrganizationSignupRequest;
import com.wexl.retail.organization.dto.*;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.util.MobileAppUtil;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
public class SuperOrgController {

  private final CurriculumService curriculumService;
  private final OrganizationAuthService organizationAuthService;
  private final SectionService sectionService;
  private final ChildOrgService childOrgService;
  private final DashboardService dashboardService;
  private final NoofOrgsandUsersbyMonthService noofOrgsandUsersbyMonthService;
  private final MlpAnalysisByInstituteService mlpAnalysisByInstituteService;
  private final ScheduleInstAttendanceService scheduleInstAttendanceService;
  private final StudentSignupService signupService;

  @IsManagerOrOrgAdmin
  @PostMapping("orgs/wexl-internal/all-orgs")
  public ResponseEntity<Object> createOrganization(
      @Valid @RequestBody OrganizationSignupRequest organizationSignupRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    return organizationAuthService.createOrganization(
        requestComingFromMobileApp, organizationSignupRequest);
  }

  @IsOrgAdmin
  @GetMapping("orgs/wexl-internal/all-orgs/{childOrg}/curriculum")
  public ResponseEntity<List<EduBoard>> getCurriculumForAllOrgs(
      @PathVariable("childOrg") String childOrgSlug) {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(curriculumService.getBoardsHierarchy(childOrgSlug));
  }

  @IsOrgAdmin
  @GetMapping("orgs/wexl-internal/all-orgs/{childOrg}/sections")
  public List<SectionEntityDto.Response> getSectionsForAllOrgs(
      @RequestParam(required = false) String grade, @PathVariable("childOrg") String childOrgSlug) {

    if (Objects.nonNull(grade)) {
      return sectionService.getSectionsByGrade(childOrgSlug, grade);
    }
    return sectionService.getAllSections(childOrgSlug, false);
  }

  @IsManagerOrOrgAdmin
  @GetMapping("orgs/wexl-internal/all-orgs")
  public ChildOrgs getParentsPublishers(
      @RequestParam(value = "is_parent", defaultValue = "false") Boolean isParent,
      @RequestParam(value = "is_publisher", defaultValue = "false") Boolean isPublisher) {
    return childOrgService.getAllParentsAndPublishers(isParent, isPublisher);
  }

  @IsManagerOrOrgAdmin
  @GetMapping("/orgs/wexl-internal/all-orgs/{orgSlug}")
  public OrganizationResponse getOrgDetails(@PathVariable String orgSlug) {
    return organizationAuthService.getOrganization(orgSlug);
  }

  @GetMapping("/orgs/wexl-internal/all-orgs/{orgSlug}/activity")
  public ServicesCount getTotalServicesUsed(
      @PathVariable String orgSlug,
      @RequestParam("from_date") long fromDate,
      @RequestParam("to_date") long toDate) {
    try {
      return dashboardService.getServicesUsage(orgSlug, fromDate, toDate);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ServicesGet.Organization", e);
    }
  }

  @PutMapping("orgs/wexl-internal/all-orgs/{orgSlug}")
  public ResponseEntity<OrganizationResponse> editOrganization(
      @PathVariable String orgSlug,
      @RequestBody OrganizationSignupRequest organizationSignupRequest) {
    try {
      return ResponseEntity.ok(
          organizationAuthService.editOrganization(orgSlug, organizationSignupRequest));
    } catch (Exception exception) {
      log.error("Failed to update organization", exception);
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "error.organizationUpdate.Failed", exception);
    }
  }

  @IsOrgAdmin
  @GetMapping("/orgs/wexl-internal/orgs-users-bymonth")
  public List<OrgsandUsersbyMonthResponse> getNooforgs() {
    return noofOrgsandUsersbyMonthService.getOrgsandUsersbyMonth();
  }

  @GetMapping("orgs/wexl-internal/all-orgs/users-analytics")
  public ResponseEntity<UserAnalyticsResponse> getUserAnalytics(
      @RequestParam(defaultValue = "1") int timePeriod) {
    try {
      return ResponseEntity.ok(organizationAuthService.getUserAnalytics(timePeriod));
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.User.Analytics", exception);
    }
  }

  @IsOrgAdmin
  @GetMapping("/org/wexl-internal/mlp-analysis-institute")
  public List<MlpAnalysisByInstituteResponse> getDetailsTimeSpendByMonth(
      @RequestParam int timePeriod,
      @RequestParam(required = false, defaultValue = "100") int limit) {
    return mlpAnalysisByInstituteService.getMlpAnalysisByInstitute(timePeriod, limit);
  }

  @IsOrgAdmin
  @GetMapping("orgs/wexl-internal/all-orgs/organization-details")
  public ResponseEntity<OrgsandUsersbyMonthResponse> getOrganizationDetails(
      @RequestParam(defaultValue = "1") int timePeriod) {
    try {
      return ResponseEntity.ok(organizationAuthService.getOrganizationDetails(timePeriod));
    } catch (Exception exception) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "error.CouldnotGetDetails", exception);
    }
  }

  @IsOrgAdmin
  @GetMapping("orgs/wexl-internal/orgs-details-by-month")
  public ResponseEntity<List<MetricsCountResponse>> getOrgsDetailByMonth(
      @RequestParam String month) {
    try {
      return ResponseEntity.ok(noofOrgsandUsersbyMonthService.getOrgWiseUserStats(month));
    } catch (Exception exception) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "error.CouldnotGetDetails", exception);
    }
  }

  @PostMapping("/orgs/wexl-internal/classroom-attendance")
  @ResponseStatus(HttpStatus.OK)
  public void markAttendanceByMigration(@RequestParam(required = false) Long date) {
    scheduleInstAttendanceService.migrateClassroomAttendance(date);
  }

  // This is just migration for ams requirement , we will remove below API after it's done
  @PostMapping("/orgs/aft620984/ams-migration")
  @ResponseStatus(HttpStatus.OK)
  public void amsPastClassroomMigration() {
    scheduleInstAttendanceService.amsClassroomAttendanceMigration();
  }

  @IsOrgAdmin
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping(value = "/orgs/wexl-internal/signup-codes", produces = MediaType.TEXT_PLAIN_VALUE)
  public String populateRegistrationCodes(
      @Valid @RequestBody SignupDto.ScratchCodeGenerationRequest codeGenerationRequest) {
    return signupService.populateRegistrationCodes(codeGenerationRequest);
  }

  @IsOrgAdmin
  @GetMapping(value = "/orgs/wexl-internal/signup-codes")
  public List<SignupDto.ScratchCodeResponse> getRegistrationCodes(
      @RequestParam("org_slug") String orgSlug,
      @RequestParam(required = false, defaultValue = "200") int limit) {
    return signupService.getRegistrationCodes(orgSlug, limit);
  }

  @IsOrgAdmin
  @GetMapping("/orgs/wexl-internal/all-orgs/{orgSlug}/summaries")
  public OrganizationDto.Response getOrgSummaries(@PathVariable String orgSlug) {
    return organizationAuthService.getOrgSummaries(orgSlug);
  }
}
