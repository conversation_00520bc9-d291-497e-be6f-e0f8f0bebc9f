package com.wexl.retail.organization.ambassador.controller;

import com.wexl.retail.commons.security.annotation.LegacyApi;
import com.wexl.retail.organization.ambassador.service.AmbassdorService;
import com.wexl.retail.organization.dto.OrganizationDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class AmbassadorController {

  private final AmbassdorService ambassdorService;

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/public/ambassadors")
  @LegacyApi
  public void registerAmbassadors(
      @RequestBody OrganizationDto.SignupRequest orgRegistrationRequest) {
    ambassdorService.registerAmbassadors(orgRegistrationRequest);
  }
}
