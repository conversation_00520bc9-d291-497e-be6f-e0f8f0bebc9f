package com.wexl.retail.notifications.model;

import com.wexl.retail.model.Student;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationMessageType;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "notification_students")
public class StudentNotification {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "notification_id")
  private Notification notification;

  @Type(JsonType.class)
  @Column(name = "test_attributes", columnDefinition = "jsonb")
  private NotificationDto.TestAttributes testAttributes;

  @ManyToOne
  @JoinColumn(name = "student_id")
  private Student student;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "sms_status")
  private NotificationMessageType smsStatus;

  @Column(name = "whatsapp_status")
  private NotificationMessageType whatsappStatus;

  @Column(name = "email_status")
  private NotificationMessageType emailStatus;
}
