package com.wexl.retail.communications.holiday.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.communications.holiday.dto.HolidayDto;
import com.wexl.retail.communications.holiday.service.HolidayService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class HolidayController {
  private final HolidayService holidayService;

  @IsOrgAdminOrTeacher
  @PostMapping("/teachers/{teacherAuthId}/holidays")
  @ResponseStatus(HttpStatus.CREATED)
  public void createHolidayNotification(
      @RequestBody HolidayDto.HolidayRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId) {

    holidayService.createHolidayNotification(orgSlug, request, teacherAuthId);
  }

  @GetMapping("/teachers/{teacherAuthId}/holidays")
  public HolidayDto.HolidayResponse getHolidayNotification(
      @PathVariable String orgSlug, @PathVariable("teacherAuthId") String teacherAuthId) {

    return holidayService.getHolidayNotifications(orgSlug, teacherAuthId);
  }

  @PutMapping("/teachers/{teacherAuthId}/holidays/{notificationId}")
  public void updateHolidayNotification(
      @PathVariable String orgSlug,
      @PathVariable("notificationId") Long notificationId,
      @RequestBody HolidayDto.HolidayRequest request) {
    holidayService.updateHolidayNotification(orgSlug, request, notificationId);
  }

  @GetMapping("/students/{studentAuthId}/holidays")
  public HolidayDto.StudentHolidayResponse getHolidayNotificationForStudent(
      @PathVariable String orgSlug, @PathVariable("studentAuthId") String studentAuthId) {
    return holidayService.getStudentHolidayNotifications(orgSlug, studentAuthId);
  }
}
