package com.wexl.retail.organization.dashboard;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.dto.MetricsCountByOrg;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ChildOrgService {

  private final OrganizationRepository organizationRepository;
  private final DateTimeUtil dateTimeUtil;
  private final UserRepository userRepository;
  private final MlpRepository mlpRepository;
  private final StrapiService strapiService;
  private final AuthService authService;

  public ChildOrgs getAllParentsAndPublishers(Boolean isParent, Boolean isPublisher) {
    List<ChildOrgsInfo> childOrgsInfos = new ArrayList<>();
    List<Organization> parentsAndPublishers = new ArrayList<>();
    User user = authService.getUserDetails();
    var isOrgAdmin = AuthUtil.isOrgAdmin(user);

    if (Boolean.TRUE.equals(isParent) && Boolean.FALSE.equals(isPublisher)) {
      parentsAndPublishers = organizationRepository.getAllParents();
    }
    if (Boolean.TRUE.equals(isPublisher) && Boolean.FALSE.equals(isParent)) {
      parentsAndPublishers = organizationRepository.getAllPubishers();
    }
    if (Boolean.TRUE.equals(isParent) && Boolean.TRUE.equals(isPublisher)) {
      parentsAndPublishers = organizationRepository.getParentsPublishers(isParent, isPublisher);
    }
    if (Boolean.FALSE.equals(isParent) && Boolean.FALSE.equals(isPublisher)) {
      if (isOrgAdmin) {
        parentsAndPublishers = organizationRepository.findAllOrgs();
      } else {
        parentsAndPublishers = organizationRepository.findAllByCreatedByOrderByCreatedAtDesc(user);
      }
    }
    List<String> orgSlugs = new ArrayList<>();
    parentsAndPublishers.forEach(data -> orgSlugs.add(data.getSlug()));
    List<MetricsCountByOrg> teachersCountByOrg = userRepository.getTeacherCountByOrgSlug(orgSlugs);

    Map<String, Integer> teacherCountByOrgMap = new HashMap<>();
    teachersCountByOrg.forEach(
        teacherCount ->
            teacherCountByOrgMap.put(teacherCount.getOrgSlug(), teacherCount.getCount()));

    List<MetricsCountByOrg> studentsCountyOrg = userRepository.getStudentCountByOrgSlug(orgSlugs);
    Map<String, Integer> studentCountByOrgMap = new HashMap<>();
    studentsCountyOrg.forEach(
        studentCount ->
            studentCountByOrgMap.put(studentCount.getOrgSlug(), studentCount.getCount()));
    List<MetricsCountByOrg> mlpCountByOrg = mlpRepository.getMlpCountByorgs(orgSlugs);
    Map<String, Integer> mlpCountByOrgMap = new HashMap<>();
    mlpCountByOrg.forEach(
        mlpCount -> mlpCountByOrgMap.put(mlpCount.getOrgSlug(), mlpCount.getCount()));

    List<com.wexl.retail.model.Organization> orgsFromContent = strapiService.getAllOrganizations();

    Map<String, List<String>> boardsByOrg = new HashMap<>();

    orgsFromContent.forEach(
        organization -> {
          List<String> boards =
              organization.getSettings().getBoards().stream().map(EduBoard::getSlug).toList();
          boardsByOrg.put(organization.getSlug(), boards);
        });
    for (Organization org : parentsAndPublishers) {
      childOrgsInfos.add(
          ChildOrgsInfo.builder()
              .orgName(org.getName())
              .startDate(dateTimeUtil.convertIso8601ToEpoch(org.getCreatedAt().toLocalDateTime()))
              .status(Objects.isNull(org.getDeletedAt()) ? "ACTIVE" : "INACTIVE")
              .boards(boardsByOrg.get(org.getSlug()))
              .totalTeachers(teacherCountByOrgMap.get(org.getSlug()))
              .totalStudents(studentCountByOrgMap.get(org.getSlug()))
              .totalMlps(mlpCountByOrgMap.get(org.getSlug()))
              .orgSlug(org.getSlug())
              .isParent(org.getIsParent())
              .isPublisher(org.getIsPublisher())
              .isSelfSignUp(org.getSelfSignup())
              .build());
    }

    return ChildOrgs.builder()
        .orgsCount(parentsAndPublishers.size())
        .studentsCount(userRepository.getStudentCountofAllOrgs())
        .teachersCount(userRepository.getTeacherCountOfAllOrgs())
        .childOrgsInfos(childOrgsInfos)
        .build();
  }

  public Organization findFirstMatchingAbbreviation(String abbreviation) {

    List<Organization> organizationList =
        organizationRepository.findByAbbreviationWithIgnoreCase(abbreviation);
    if (organizationList.isEmpty()) {
      return null;
    }
    return organizationList.getFirst();
  }
}
