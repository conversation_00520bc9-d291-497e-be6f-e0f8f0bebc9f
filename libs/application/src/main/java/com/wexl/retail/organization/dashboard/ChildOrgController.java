package com.wexl.retail.organization.dashboard;

import com.wexl.retail.commons.caching.CacheConstants;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.dto.response.SectionResponse;
import com.wexl.retail.section.service.SectionService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ChildOrgController {

  private final CurriculumService curriculumService;
  private final SectionService sectionService;

  @GetMapping("/orgs/{org}/child-orgs/{childOrg}/curriculum")
  public ResponseEntity<List<EduBoard>> getCurriculum(
      @PathVariable("childOrg") String childOrgSlug) {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(curriculumService.getBoardsHierarchy(childOrgSlug));
  }

  @GetMapping("/orgs/{org}/child-orgs/{childOrg}/sections")
  public List<SectionEntityDto.Response> getSections(
      @RequestParam(required = false) String grade,
      @RequestParam(required = false) String board,
      @PathVariable("childOrg") String childOrgSlug) {
    if (Objects.nonNull(grade)) {
      if (Objects.nonNull(board)) {
        return sectionService.getSectionsByBoardAndGrade(childOrgSlug, grade, board);
      }
      return sectionService.getSectionsByGrade(childOrgSlug, grade);
    }
    return sectionService.getAllSections(childOrgSlug, false);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("orgs/{orgSlug}/child-orgs-sections")
  public List<SectionResponse> getSectionsOfChildOrgsByGrade(
      @RequestParam(value = "grade", required = false) String gradeSlug,
      @RequestParam("childOrg") List<String> childOrgs) {

    return sectionService.getSectionsOfChildOrgsByGrade(gradeSlug, childOrgs);
  }
}
