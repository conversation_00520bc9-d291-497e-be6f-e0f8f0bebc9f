package com.wexl.retail.calenderevent.repository;

import com.wexl.retail.calenderevent.dto.CalenderEventQueryResult;
import com.wexl.retail.calenderevent.model.CalendarEventUser;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CalendarEventUserRepository extends JpaRepository<CalendarEventUser, Long> {

  @Query(
      value =
          """
                  SELECT
                   id, title, description, dueDate, grade, sectionUuid, colour, type, visibility, array_agg(COALESCE(assetSlugs, 'null')) AS assetSlugs
               FROM (
                   SELECT
                       ce.id, ce.title, ce.description, ce.due_date AS dueDate, ce.grade_slug AS grade, ce.section_uuid AS sectionUuid,
                       ce.colour, CASE WHEN ce."type" = '0' THEN 'EVENT' ELSE 'LESSON_PLANNER' END AS type,
                       CASE WHEN ce.visibility = '0' THEN 'STUDENTS' WHEN ce.visibility = '1' THEN 'TEACHERS' ELSE 'ALL' END AS visibility,
                       assets.assetSlugs
                   FROM calender_event_users ceu
                   JOIN calender_events ce ON ceu.calendar_event_id = ce.id
                   LEFT JOIN LATERAL (SELECT jsonb_array_elements_text(ce.assets) AS assetSlugs) assets ON true
                   WHERE ce.academic_year_slug = :latestAcademicYear
                     AND (cast((:userId) as varChar) IS NULL OR ceu.user_id IN (:userId))
                     AND ce.org_slug = :orgSlug
                     AND DATE(ce.due_date) BETWEEN :fDate AND :toDate

                   UNION

                   SELECT
                       ce.id, ce.title, ce.description, ce.due_date AS dueDate, ce.grade_slug AS grade, ce.section_uuid AS sectionUuid,
                       ce.colour, CASE WHEN ce."type" = '0' THEN 'EVENT' ELSE 'LESSON_PLANNER' END AS type,
                       CASE WHEN ce.visibility = '0' THEN 'STUDENTS' WHEN ce.visibility = '1' THEN 'TEACHERS' ELSE 'ALL' END AS visibility,
                       assets.assetSlugs
                   FROM calender_events ce
                   LEFT JOIN LATERAL (SELECT jsonb_array_elements_text(ce.assets) AS assetSlugs) assets ON true
                   WHERE ce."type" = '0'
                     AND ce.academic_year_slug = :latestAcademicYear
                     AND ce.org_slug = :orgSlug
                     AND DATE(ce.due_date) BETWEEN :fDate AND :toDate
               ) a
               GROUP BY
                   id, title, description, dueDate, grade, sectionUuid, colour, type, visibility;

                           """,
      nativeQuery = true)
  List<CalenderEventQueryResult> getCalenderDataByUserId(
      String orgSlug,
      String latestAcademicYear,
      LocalDateTime fDate,
      LocalDateTime toDate,
      Long userId);
}
