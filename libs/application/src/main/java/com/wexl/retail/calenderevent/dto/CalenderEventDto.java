package com.wexl.retail.calenderevent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.notifications.dto.NotificationDto;
import java.util.List;
import lombok.Builder;

public record CalenderEventDto() {

  public record LessonPlannerRequest(
      String title,
      String description,
      Long date,
      String colour,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section_uuid") String sectionUuid,
      CalenderEventType type,
      CalenderEventVisibility visibility,
      @JsonProperty("students") List<Long> studentsList,
      @JsonProperty("teachers") List<Long> teachersList,
      @JsonProperty("assets") List<String> assetSlugs,
      List<String> attachment) {}

  @Builder
  public record Response(
      Long id,
      String title,
      String description,
      Long date,
      String colour,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("assets") List<AssetResponse> assets,
      @JsonProperty("calender_event_user_ids") List<Long> calenderEventUserIds,
      CalenderEventType type,
      CalenderEventVisibility visibility) {}

  @Builder
  public record AssetResponse(
      @JsonProperty("asset_type") String assetType,
      @JsonProperty("asset_link") String assetLink,
      @JsonProperty("file_type") String fileType,
      @JsonProperty("asset_name") String assetName,
      @JsonProperty("page_type") String pageType,
      @JsonProperty("asset_slug") String assetSlug,
      @JsonProperty("link_type") String linkType,
      @JsonProperty("link_source") String linkSource,
      @JsonProperty("link_path") String linkPath) {}

  @Builder
  public record CalenderUserResponse(
      String authUserId, String name, Boolean isStudent, Boolean isTeacher) {}

  public record EventRequest(
      String title,
      String description,
      Long date,
      String colour,
      CalenderEventType type,
      String authUserId) {}

  @Builder
  public record CalenderResponse(Long date, List<Data> data) {}

  @Builder
  public record Data(String type, String colour) {}

  @Builder
  public record StudentCommunicationResponse(
      @JsonProperty("calender_communications")
          List<NotificationDto.NotificationResponse> communicationNotifications) {}
}
