package com.wexl.retail.messagetemplate.category.controller;

import com.wexl.retail.messagetemplate.category.dto.CategoryType;
import com.wexl.retail.messagetemplate.category.dto.MessageTemplateCategoryDto;
import com.wexl.retail.messagetemplate.category.service.MessageTemplateCategoryService;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("orgs/{orgSlug}/message-template-categories")
public class MessageTemplateCategoryController {

  @Autowired MessageTemplateCategoryService messageTemplateCategoryService;

  @PostMapping()
  public void createMessageTemplateCategory(
      @PathVariable String orgSlug, @RequestBody MessageTemplateCategoryDto.Request request) {
    messageTemplateCategoryService.createMessageTemplateCategory(orgSlug, request);
  }

  @GetMapping()
  public List<MessageTemplateCategoryDto.Response> getMessageTemplatesCategory(
      @PathVariable String orgSlug, @RequestParam Optional<CategoryType> type) {
    return messageTemplateCategoryService.getMessageTemplatesCategory(orgSlug, type);
  }

  @PutMapping("/{templateCategoryId}")
  public void editMessageTemplateCategory(
      @PathVariable String orgSlug,
      @PathVariable Long templateCategoryId,
      @RequestBody MessageTemplateCategoryDto.Request request) {
    messageTemplateCategoryService.editMessageTemplateCategory(
        orgSlug, templateCategoryId, request);
  }

  @DeleteMapping("/{templateCategoryId}")
  public void deleteMessageTemplateCategory(
      @PathVariable String orgSlug, @PathVariable Long templateCategoryId) {
    messageTemplateCategoryService.deleteMessageTemplateCategory(orgSlug, templateCategoryId);
  }
}
