package com.wexl.retail.lessonplanner.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "teacher_time_table")
public class TeacherTimeTable extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "section_uuid")
  private String sectionUuid;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "teacherTimeTable", cascade = CascadeType.ALL)
  private List<TeacherTimeTableDetail> teacherTimeTableDetails;
}
