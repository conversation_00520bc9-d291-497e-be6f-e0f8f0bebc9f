package com.wexl.retail.relationship.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record RelationshipDto() {

  public record Request(
      @JsonProperty("student_authId") String studentAuthId,
      @JsonProperty("relationship_type") String relationshipType) {}

  @Builder
  public record Response(
      @JsonProperty("student_authId") String studentAuthId,
      @JsonProperty("relationship_type") String relationshipType,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("student_name") String name) {}

  @Builder
  public record Types(
      Long id,
      @JsonProperty("a_isto_b") String aIsToB,
      @JsonProperty("b_isto_a") String bIsToA,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("is_primary") Boolean isPrimary) {}
}
