package com.wexl.retail.communications.holiday.service;

import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.communications.holiday.dto.HolidayDto;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.UserRepository;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HolidayService {
  private final NotificationsService notificationService;
  private final UserRepository userRepository;
  private final NotificationRepository notificationRepository;
  private final com.wexl.retail.commons.util.DateTimeUtil dateTimeUtil;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final EventNotificationService eventNotificationService;

  public void createHolidayNotification(
      String orgSlug, HolidayDto.HolidayRequest request, String teacherAuthId) {
    long createdAt =
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.message())
            .gradeSlugs(request.gradeSlugs())
            .attachment(request.attachment())
            .fromDate(request.fromDate())
            .toDate(request.toDate())
            .link(request.link())
            .notificationType(
                request.isAllGrades()
                    ? com.wexl.retail.notifications.model.NotificationType.ORGANIZATION
                    : NotificationType.GRADE)
            .orgSlugs(List.of(orgSlug))
            .feature(CommunicationFeature.HOLIDAY)
            .build();
    if (request.isAllGrades()) {
      notificationService.createNotificationForOrganization(
          orgSlug, notificationRequest, teacherAuthId, false, createdAt);
    } else {
      notificationService.createNotificationByGrade(orgSlug, notificationRequest, teacherAuthId);
    }
    eventNotificationService.sendPushNotificationWithGrade(
        orgSlug, notificationRequest.message(), notificationRequest.title(), request.gradeSlugs());
    LocalDate fromDate =
        Instant.ofEpochMilli(request.fromDate()).atZone(ZoneId.systemDefault()).toLocalDate();
    LocalDate toDate =
        Instant.ofEpochMilli(request.toDate()).atZone(ZoneId.systemDefault()).toLocalDate();
    Integer startDateId = Integer.parseInt(fromDate.format(DateTimeFormatter.BASIC_ISO_DATE));
    Integer endDateId = Integer.parseInt(toDate.format(DateTimeFormatter.BASIC_ISO_DATE));
    List<SectionAttendance> attendanceList =
        sectionAttendanceRepository.findAttendanceByDateRangeAndGrades(
            orgSlug, startDateId, endDateId, request.gradeSlugs());
    attendanceList.forEach(attendance -> attendance.setStatus(CompletionStatus.HOLIDAY));
    sectionAttendanceRepository.saveAll(attendanceList);
  }

  public HolidayDto.HolidayResponse getHolidayNotifications(String orgSlug, String teacherAuthId) {

    var holidayNotifications =
        notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
            orgSlug, CommunicationFeature.HOLIDAY);
    var notificationResponse =
        notificationService.buildNotificationResponse(holidayNotifications, 100);
    return HolidayDto.HolidayResponse.builder().holidayResponse(notificationResponse).build();
  }

  public HolidayDto.StudentHolidayResponse getStudentHolidayNotifications(
      String orgSlug, String studentAuthId) {
    var student =
        userRepository.findByAuthUserIdAndOrganization(studentAuthId, orgSlug).getStudentInfo();
    var studentHolidayNotifications =
        notificationRepository
            .findByOrgSlugAndFeatureAndStudentNotifications_StudentAndDeletedAtIsNullOrderByCreatedAtDesc(
                orgSlug, CommunicationFeature.HOLIDAY, student);
    var notificationResponse =
        notificationService.buildNotificationResponse(studentHolidayNotifications, 100);
    return HolidayDto.StudentHolidayResponse.builder()
        .holidayResponse(notificationResponse)
        .build();
  }

  public void updateHolidayNotification(
      String orgSlug, HolidayDto.HolidayRequest request, Long notificationId) {
    var notification = notificationService.getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    notification.setTitle(request.title());
    notification.setMessage(request.message());
    notification.setAttachments(request.attachment());
    notification.setLink(request.link());
    notification.setFromDate(
        Objects.nonNull(request.fromDate())
            ? dateTimeUtil.convertEpochToTimestamp(request.fromDate())
            : null);
    notification.setToDate(
        Objects.nonNull(request.toDate())
            ? dateTimeUtil.convertEpochToTimestamp(request.toDate())
            : null);
    notificationRepository.save(notification);
  }
}
