package com.wexl.retail.messagetemplate.controller;

import com.wexl.retail.messagetemplate.dto.MessageTemplateDto;
import com.wexl.retail.messagetemplate.dto.MessageType;
import com.wexl.retail.messagetemplate.service.MessageTemplateService;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("orgs/{orgSlug}/message-templates")
public class MessageTemplateController {
  @Autowired private MessageTemplateService messageTemplateService;

  @PostMapping()
  public void createMessageTemplate(
      @PathVariable String orgSlug, @RequestBody MessageTemplateDto.Request request) {
    messageTemplateService.createMessageTemplate(orgSlug, request);
  }

  @GetMapping()
  public List<MessageTemplateDto.Response> getMessageTemplates(
      @PathVariable String orgSlug, @RequestParam Optional<MessageType> type) {
    return messageTemplateService.getMessageTemplates(orgSlug, type);
  }

  @PutMapping("/{templateId}")
  public void editMessageTemplate(
      @PathVariable String orgSlug,
      @PathVariable Long templateId,
      @RequestBody MessageTemplateDto.Request request) {
    messageTemplateService.editMessageTemplate(orgSlug, templateId, request);
  }
}
