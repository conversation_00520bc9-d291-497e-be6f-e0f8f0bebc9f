package com.wexl.retail.organization.dashboard;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ChildOrgs {
  private Integer orgsCount;
  private Integer studentsCount;
  private Integer teachersCount;
  private Integer totalMlpCount;
  private Integer mlpsCreatedYesterdayCount;
  private Integer mlpsCreatedDayBeforeYesterday;
  private List<ChildOrgsInfo> childOrgsInfos;
}
