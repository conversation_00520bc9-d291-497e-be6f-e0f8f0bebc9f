package com.wexl.retail.syllabustracking.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;
import static com.wexl.retail.syllabustracking.model.SyllabusTrackingStatus.COMPLETED;
import static com.wexl.retail.syllabustracking.model.SyllabusTrackingStatus.NOT_STARTED;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Subject;
import com.wexl.retail.organization.dto.CurriculumGrade;
import com.wexl.retail.organization.dto.CurriculumSubject;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.syllabustracking.dto.ChapterResponse;
import com.wexl.retail.syllabustracking.dto.SyllabusDto;
import com.wexl.retail.syllabustracking.model.SyllabusTracking;
import com.wexl.retail.syllabustracking.publisher.SyllabusTrackingEventPublisher;
import com.wexl.retail.syllabustracking.repository.SyllabusTrackingRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SyllabusTrackingService {

  private final SyllabusTrackingRepository syllabusTrackingRepository;

  private final SectionRepository sectionRepository;
  private final StrapiService strapiService;
  private final DateTimeUtil dateTimeUtil;

  private final OrgSettingsService orgSettingsService;

  private final ContentService contentService;

  private final AuthService authService;

  private final TeacherSectionRepository teacherSectionRepository;

  private final SyllabusTrackingEventPublisher syllabusTrackingEventPublisher;

  private final TeacherRepository teacherRepository;
  private final CurriculumService curriculumService;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final UserService userService;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  public void initiateSyllabusTracking(String orgSlug) {
    var organization = orgSettingsService.validateOrganizaiton(orgSlug);

    var boards = organization.getCurriculum().getBoards();
    boards.forEach(
        board -> {
          List<CurriculumGrade> grades = board.getGrades();
          List<SubTopicResponse> subTopicResponses =
              validateSyllabusTrackingByGrades(orgSlug, board.getSlug(), grades);
          if (subTopicResponses == null || subTopicResponses.isEmpty()) {
            return;
          }

          List<SyllabusTracking> syllabusTrackings = new ArrayList<>();
          board
              .getGrades()
              .forEach(
                  grade -> {
                    var sections =
                        sectionRepository.getSectionsUsingGradeSlugs(
                            List.of(grade.getSlug()), organization.getSlug());

                    List<SubTopicResponse> gradeWiseSubtopics =
                        subTopicResponses.stream()
                            .filter(subTopic -> grade.getSlug().equals(subTopic.getGradeSlug()))
                            .toList();
                    sections.forEach(
                        section ->
                            syllabusTrackings.addAll(
                                buildSyllabusTracking(section, gradeWiseSubtopics)));
                  });
          syllabusTrackingRepository.saveAll(syllabusTrackings);
        });
  }

  private List<SubTopicResponse> validateSyllabusTrackingByGrades(
      String orgSlug, String boardSlug, List<CurriculumGrade> grades) {
    List<String> gradeSlugs = grades.stream().map(CurriculumGrade::getSlug).toList();
    List<String> subjectSlugs =
        grades.stream().map(CurriculumGrade::getSubjects).toList().stream()
            .flatMap(List::stream)
            .map(CurriculumSubject::getSlug)
            .toList();
    List<SubTopicResponse> subTopics =
        grades.stream()
            .map(
                grade -> {
                  var subjects =
                      grade.getSubjects().stream().map(CurriculumSubject::getSlug).toList();
                  return contentService.getSubtopicsByBoardAndGradeAndSubject(
                      orgSlug, boardSlug, List.of(grade.getSlug()), subjects);
                })
            .flatMap(Collection::stream)
            .toList();

    List<SyllabusTracking> existingRecords =
        syllabusTrackingRepository
            .findAllByOrgSlugAndBoardSlugAndGradeSlugInAndSubjectSlugInAndDeletedAtIsNull(
                orgSlug, boardSlug, gradeSlugs, subjectSlugs);

    if (existingRecords.isEmpty()) {
      return subTopics;
    }
    updateChapterAndSubtopicNames(new HashSet<>(subTopics), existingRecords);
    var inactivatedChapterSubtopics =
        existingRecords.stream()
            .filter(
                subtopic ->
                    !subTopics.stream()
                        .map(SubTopicResponse::getSlug)
                        .toList()
                        .contains(subtopic.getSubtopicSlug()))
            .toList();
    syllabusTrackingRepository.deleteAll(inactivatedChapterSubtopics);

    Set<String> existingSectionUuids =
        existingRecords.stream().map(SyllabusTracking::getSectionUuid).collect(Collectors.toSet());
    List<Section> sections = sectionRepository.getSectionsUsingGradeSlugs(gradeSlugs, orgSlug);

    List<Section> newSections =
        sections.stream()
            .filter(sec -> !existingSectionUuids.contains(sec.getUuid().toString()))
            .toList();

    Set<String> existingSubtopicSlugs =
        existingRecords.stream().map(SyllabusTracking::getSubtopicSlug).collect(Collectors.toSet());

    if (!newSections.isEmpty() && !existingSectionUuids.isEmpty()) {
      validateSyllabusTrackingBySection(newSections, subTopics);
    }
    return new ArrayList<>(
        subTopics.stream()
            .filter(subTopic -> !existingSubtopicSlugs.contains(subTopic.getSlug()))
            .toList());
  }

  private void updateChapterAndSubtopicNames(
      Set<SubTopicResponse> subTopics, List<SyllabusTracking> existingRecords) {
    Map<String, SubTopicResponse> subTopicMap =
        subTopics.stream()
            .collect(Collectors.toMap(SubTopicResponse::getSlug, Function.identity()));

    for (SyllabusTracking record : existingRecords) {
      SubTopicResponse subTopic = subTopicMap.get(record.getSubtopicSlug());
      if (subTopic != null) {
        record.setChapterName(subTopic.getChapterName());
        record.setSubtopicName(subTopic.getName());
      }
    }
    syllabusTrackingRepository.saveAll(existingRecords);
  }

  private void validateSyllabusTrackingBySection(
      List<Section> newSections, List<SubTopicResponse> subTopics) {
    List<SyllabusTracking> syllabusTrackingList = new ArrayList<>();
    for (Section section : newSections) {
      Set<SyllabusTracking> existingRecords =
          syllabusTrackingRepository.findAllByOrgSlugAndBoardSlugAndGradeSlugAndDeletedAtIsNull(
              section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
      Set<String> existingSubtopicSlugs =
          existingRecords.stream()
              .map(SyllabusTracking::getSubtopicSlug)
              .collect(Collectors.toSet());
      List<SubTopicResponse> filteredSubtopics =
          subTopics.stream()
              .filter(subTopic -> existingSubtopicSlugs.contains(subTopic.getSlug()))
              .toList();
      syllabusTrackingList.addAll(buildSyllabusTracking(section, filteredSubtopics));
    }
    syllabusTrackingRepository.saveAll(syllabusTrackingList);
  }

  private List<SyllabusTracking> buildSyllabusTracking(
      Section section, List<SubTopicResponse> subTopicResponses) {
    return subTopicResponses.stream()
        .map(
            subtopic ->
                SyllabusTracking.builder()
                    .subtopicSlug(subtopic.getSlug())
                    .subtopicName(subtopic.getName())
                    .chapterName(subtopic.getChapterName())
                    .chapterSlug(subtopic.getChapterSlug())
                    .sectionUuid(section.getUuid().toString())
                    .sectionName(section.getName())
                    .orgSlug(section.getOrganization())
                    .gradeSlug(section.getGradeSlug())
                    .gradeName(section.getGradeName())
                    .subjectName(subtopic.getSubjectName())
                    .subjectSlug(subtopic.getSubjectSlug())
                    .academicYearSlug(latestAcademicYear)
                    .status(NOT_STARTED)
                    .boardSlug(section.getBoardSlug())
                    .build())
        .toList();
  }

  public void updateSyllabusTrackingStatus(
      String orgSlug, long id, SyllabusDto.SyllabusStatusRequest request) {
    var syllabusTracking = validateSyllabusTrackingBySubtopic(orgSlug, id, request.subtopics());

    var teacherDetails = authService.getTeacherDetails();
    syllabusTracking.setStatus(request.status());
    syllabusTracking.setUpdatedBy(teacherDetails.getId());
    syllabusTracking.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    var latestSyllabusTracking = syllabusTrackingRepository.save(syllabusTracking);
    syllabusTrackingEventPublisher.publishExamCompletion(latestSyllabusTracking);
  }

  private SyllabusTracking validateSyllabusTrackingBySubtopic(
      String orgSlug, long id, String subtopic) {
    return syllabusTrackingRepository
        .findByIdAndOrgSlugAndSubtopicSlug(id, orgSlug, subtopic)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.SyllabusTrackingNotFound"));
  }

  public SyllabusDto.SyllabusTrackingResponse getTeacherSyllabus(
      String orgSlug, String boardSlug, String section, String subject) {
    var user = authService.getUserDetails();

    if (!syllabusTrackingRepository.existsByOrgSlug(orgSlug)) {
      if (AuthUtil.isOrgAdmin(user)) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.InitiateSyllabusTrackings");
      }
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SyllabusTrackingNotInitiated");
    }

    var syllabusTrackings =
        syllabusTrackingRepository
            .findByOrgSlugAndBoardSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNullOrderByIdAsc(
                orgSlug, boardSlug, section, subject);

    if (syllabusTrackings.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SyllabusTrackingError");
    }
    return buildSyllabusResponse(syllabusTrackings);
  }

  private SyllabusDto.SyllabusTrackingResponse buildSyllabusResponse(
      List<SyllabusTracking> syllabusTrackings) {
    return SyllabusDto.SyllabusTrackingResponse.builder()
        .subjectSlug(syllabusTrackings.getFirst().getSubjectSlug())
        .subjectName(syllabusTrackings.getFirst().getSubjectName())
        .chapters(buildChapterResponse(syllabusTrackings))
        .build();
  }

  private List<SyllabusDto.ChapterResponse> buildChapterResponse(
      List<SyllabusTracking> syllabusTrackings) {
    Map<String, List<SyllabusTracking>> chapterTrackingsMap =
        syllabusTrackings.stream().collect(Collectors.groupingBy(SyllabusTracking::getChapterSlug));

    List<SyllabusDto.ChapterResponse> chapterResponses = new ArrayList<>();
    chapterTrackingsMap.forEach(
        (chapter, trackings) -> {
          var response =
              SyllabusDto.ChapterResponse.builder()
                  .chapterName(trackings.getFirst().getChapterName())
                  .chapterSlug(trackings.getFirst().getChapterSlug())
                  .subtopics(buildSubtopicResponse(trackings))
                  .build();
          chapterResponses.add(response);
        });
    chapterResponses.sort(Comparator.comparing(SyllabusDto.ChapterResponse::chapterName));
    return chapterResponses;
  }

  private List<SyllabusDto.SubtopicResponse> buildSubtopicResponse(
      List<SyllabusTracking> syllabusTrackings) {
    return syllabusTrackings.stream()
        .map(
            st ->
                SyllabusDto.SubtopicResponse.builder()
                    .id(st.getId())
                    .subtopicName(st.getSubtopicName())
                    .subtopicSlug(st.getSubtopicSlug())
                    .status(st.getStatus())
                    .completedDate(
                        Objects.isNull(st.getUpdatedAt())
                            ? null
                            : convertIso8601ToEpoch(st.getUpdatedAt().toLocalDateTime()))
                    .build())
        .sorted(Comparator.comparing(SyllabusDto.SubtopicResponse::subtopicName))
        .toList();
  }

  public List<GenericMetricResponse> getSyllabusDetails(
      Long date, String org, String section, String boardSlug) {
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    var sections = sectionRepository.findByUuid(UUID.fromString(section)).orElseThrow();
    List<EduBoard> subjects = curriculumService.getBoardsHierarchy(org);
    List<EduBoard> filteredByBoard =
        subjects.stream().filter(subject -> subject.getSlug().equals(boardSlug)).toList();
    Grade matchedGrade =
        filteredByBoard.stream()
            .flatMap(board -> board.getGrades().stream())
            .filter(grade -> grade.getSlug().equals(sections.getGradeSlug()))
            .findFirst()
            .orElseThrow();

    for (Subject subject : matchedGrade.getSubjects()) {
      List<String> chapterList = new ArrayList<>();
      Map<String, Object> data = new HashMap<>();
      var syllabusTrackings =
          syllabusTrackingRepository
              .findAllByOrgSlugAndSectionUuidAndBoardSlugAndSubjectSlugInAndDeletedAtIsNull(
                  org, section, boardSlug, Collections.singletonList(subject.getSlug()));
      var chapters =
          syllabusTrackings.stream().map(SyllabusTracking::getChapterSlug).distinct().toList();
      var subjectName = strapiService.getSubjectNameBySlug(subject.getSlug());
      for (String chapter : chapters) {
        var subtopics =
            syllabusTrackings.stream()
                .filter(chap -> chap.getChapterSlug().equals(chapter))
                .toList();
        var notStartedCount =
            subtopics.stream()
                .filter(
                    subtopic ->
                        subtopic.getStatus().equals(COMPLETED)
                            && date >= dateTimeUtil.convertTimeStampToLong(subtopic.getUpdatedAt()))
                .toList();
        if (notStartedCount.size() == subtopics.size()) {
          chapterList.add(chapter);
        }
      }
      var percentage = ((double) chapterList.size() / chapters.size()) * 100;
      data.put("completed_chapters_count", chapterList.isEmpty() ? 0 : chapterList.size());
      data.put("subject", subjectName);
      data.put("subject_slug", subject.getSlug());
      data.put("total_chapters", chapters.size());
      data.put("percentage", Math.round(percentage) + "%");
      genericMetricResponses.add(GenericMetricResponse.builder().data(data).build());
    }
    return genericMetricResponses;
  }

  public List<SyllabusDto.ChapterCounts> getChapterCountDetails(
      String orgSlug, List<String> sectionUuid, String boardSlug, String gradeSlug) {
    List<SyllabusDto.ChapterCounts> chapterCounts = new ArrayList<>();
    sectionUuid.forEach(
        sec -> {
          var sections = sectionRepository.findByUuid(UUID.fromString(sec)).orElseThrow();
          List<EduBoard> subjects = curriculumService.getBoardsHierarchy(orgSlug);
          List<EduBoard> filteredByBoard =
              subjects.stream().filter(subject -> subject.getSlug().equals(boardSlug)).toList();
          Grade matchedGrade =
              filteredByBoard.stream()
                  .flatMap(board -> board.getGrades().stream())
                  .filter(grade -> grade.getSlug().equals(sections.getGradeSlug()))
                  .findFirst()
                  .orElseThrow();

          List<ChapterResponse> chapterDetails =
              syllabusTrackingRepository
                  .getChapterCountDetailsByOrgSlugAndSectionUuidAndBoardSlugAndGradeSlugAndDeletedAtIsNull(
                      orgSlug, sec, boardSlug, gradeSlug);

          matchedGrade
              .getSubjects()
              .forEach(
                  subject -> {
                    var chapterData =
                        chapterDetails.stream()
                            .filter(data -> data.getSubjectSlug().equals(subject.getSlug()))
                            .findFirst();

                    List<SyllabusDto.Chapters> completedChaptersList =
                        chapterData.map(data -> buildChapterDetails(data, true)).orElse(List.of());

                    List<SyllabusDto.Chapters> pendingChaptersList =
                        chapterData.map(data -> buildChapterDetails(data, false)).orElse(List.of());

                    chapterCounts.add(
                        SyllabusDto.ChapterCounts.builder()
                            .sectionName(sections.getName())
                            .subjectName(subject.getName())
                            .totalChapters(
                                chapterData.map(ChapterResponse::getTotalChapters).orElse(0L))
                            .completedChapters(
                                chapterData.map(ChapterResponse::getCompletedChapters).orElse(0L))
                            .completedChaptersList(completedChaptersList)
                            .pendingChapters(
                                chapterData.map(ChapterResponse::getPendingChapters).orElse(0L))
                            .pendingChaptersList(pendingChaptersList)
                            .build());
                  });
        });

    return chapterCounts;
  }

  private List<SyllabusDto.Chapters> buildChapterDetails(
      ChapterResponse chapterData, boolean isCompleted) {

    List<String> chapterNames =
        Optional.ofNullable(
                isCompleted
                    ? chapterData.getCompletedChapterNames()
                    : chapterData.getPendingChapterNames())
            .orElse(List.of());
    List<String> chapterSlugs =
        Optional.ofNullable(
                isCompleted
                    ? chapterData.getCompletedChapterSlugs()
                    : chapterData.getPendingChapterSlugs())
            .orElse(List.of());

    int size = Math.min(chapterNames.size(), chapterSlugs.size());

    return IntStream.range(0, size)
        .mapToObj(
            i ->
                SyllabusDto.Chapters.builder()
                    .chapterName(chapterNames.get(i))
                    .chapterSlug(chapterSlugs.get(i))
                    .build())
        .toList();
  }

  public List<SyllabusDto.subTopicDetails> getSubTopicCountDetails(
      String orgSlug,
      List<String> sectionUuid,
      String boardSlug,
      String gradeSlug,
      String subjectSlug) {
    List<SyllabusDto.subTopicDetails> subTopicDetailsList = new ArrayList<>();
    sectionUuid.forEach(
        sec -> {
          Section section = sectionRepository.findAllByUuid(UUID.fromString(sec));
          var teacherSubjects =
              teacherSubjectsRepository.findBySubjectAndSectionId(subjectSlug, section.getId());
          List<ChapterResponse> subTopicDetails =
              syllabusTrackingRepository
                  .getSubtopicCountDetailsByOrgSlugAndSectionUuidAndBoardSlugAndGradeSlugAndSubjectSlugAndDeletedAtIsNull(
                      orgSlug, sec, boardSlug, gradeSlug, subjectSlug);
          long completedSubtopicCount =
              subTopicDetails.stream().mapToLong(ChapterResponse::getCompletedSubtopics).sum();
          long pendingSubtopicCount =
              subTopicDetails.stream().mapToLong(ChapterResponse::getPendingSubtopics).sum();
          if (!teacherSubjects.isEmpty()) {
            teacherSubjects.forEach(
                ts ->
                    subTopicDetailsList.add(
                        SyllabusDto.subTopicDetails
                            .builder()
                            .teacherName(
                                userService.getNameByUserInfo(ts.getTeacher().getUserInfo()))
                            .totalSubTopics((long) subTopicDetails.size())
                            .completedSubTopicsCount(completedSubtopicCount)
                            .pendingSubTopicsCount(pendingSubtopicCount)
                            .sectionName(section.getName())
                            .completedSubTopics(buildCompletedSubTopics(subTopicDetails))
                            .pendingSubTopics(buildPendingSubTopics(subTopicDetails))
                            .build()));
          }
        });

    return subTopicDetailsList;
  }

  private List<SyllabusDto.subTopics> buildCompletedSubTopics(
      List<ChapterResponse> subTopicDetails) {
    return subTopicDetails.stream()
        .filter(data -> data.getCompletedSubtopics() > 0)
        .map(
            data ->
                SyllabusDto.subTopics
                    .builder()
                    .subTopicName(data.getSubtopicName())
                    .subTopicSlug(data.getSubtopicSlug())
                    .subjectName(data.getSubjectName())
                    .build())
        .toList();
  }

  private List<SyllabusDto.subTopics> buildPendingSubTopics(List<ChapterResponse> subTopicDetails) {
    return subTopicDetails.stream()
        .filter(data -> data.getPendingSubtopics() > 0)
        .map(
            data ->
                SyllabusDto.subTopics
                    .builder()
                    .subTopicName(data.getSubtopicName())
                    .subTopicSlug(data.getSubtopicSlug())
                    .subjectName(data.getSubjectName())
                    .build())
        .toList();
  }
}
