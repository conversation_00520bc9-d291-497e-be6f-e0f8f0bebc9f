package com.wexl.retail.notifications.resolver;

import com.wexl.retail.notifications.dto.EmailRecipientGroup;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.repository.StudentRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StudentRecipientResolver implements RecipientResolver {
  @Autowired private StudentRepository studentRepository;

  @Override
  public boolean supports(NotificationDto.SendTo sendTo) {
    return sendTo.groups() != null && sendTo.groups().contains(EmailRecipientGroup.STUDENTS.name())
        || (sendTo.individuals() != null
            && sendTo.individuals().containsKey(EmailRecipientGroup.STUDENTS))
        || (sendTo.classSelection() != null
            && sendTo.classSelection().sendTo().contains(EmailRecipientGroup.STUDENTS.name()));
  }

  @Override
  public List<Object> resolveRecipients(String orgSlug, NotificationDto.SendTo sendTo) {
    List<Object> students = new ArrayList<>();

    if (sendTo.groups() != null && sendTo.groups().contains(EmailRecipientGroup.STUDENTS.name())) {
      students.addAll(studentRepository.findByOrgSlug(orgSlug));
    }

    if (sendTo.individuals() != null
        && sendTo.individuals().containsKey(EmailRecipientGroup.STUDENTS)) {
      List<Long> studentIds = sendTo.individuals().get(EmailRecipientGroup.STUDENTS);
      students.addAll(studentRepository.findAllById(studentIds));
    }

    if (sendTo.classSelection() != null && sendTo.classSelection().sectionUuids() != null) {
      List<UUID> sectionUuids =
          sendTo.classSelection().sectionUuids().stream().map(UUID::fromString).toList();
      students.addAll(studentRepository.getStudentsBySectionUuidsAndOrgSlug(sectionUuids, orgSlug));
    }

    return students;
  }
}
