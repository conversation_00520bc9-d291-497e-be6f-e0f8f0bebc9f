package com.wexl.retail.organization.dashboard;

import static com.wexl.retail.commons.util.DateTimeUtil.findYearByMonth;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.organization.dto.MetricsCountResponse;
import com.wexl.retail.organization.dto.OrgsandUsersbyMonthResponse;
import com.wexl.retail.organization.repository.OrganizationRepository;
import java.time.LocalDate;
import java.time.Month;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class NoofOrgsandUsersbyMonthService {
  @Autowired private OrganizationRepository organizationRepository;

  public List<OrgsandUsersbyMonthResponse> getOrgsandUsersbyMonth() {

    LocalDate toDate = LocalDate.now();

    LocalDate fromDate = LocalDate.now().minusMonths(11);

    List<OrgsandUsersbyMonthResponse> nooforgsusers = new ArrayList<>();
    var noofOrgsList = organizationRepository.getOrgsbyMonth(fromDate, toDate);

    var noofUsersList = organizationRepository.getUsersbyMonth(fromDate, toDate);

    long usersCountSum = 0;
    for (var users : noofUsersList) {
      long orgCount = 0;

      var orgdata =
          noofOrgsList.stream()
              .filter(s -> s.getMonthName().trim().equals(users.getMonthName().trim()))
              .findFirst();
      if (orgdata.isPresent()) {
        orgCount = orgdata.get().getOrgsCount();
      }

      usersCountSum = usersCountSum + users.getUsersCount();
      nooforgsusers.add(
          OrgsandUsersbyMonthResponse.builder()
              .monthName(users.getMonthName())
              .orgsCount(orgCount)
              .usersCount(users.getUsersCount())
              .usersCountSum(usersCountSum)
              .build());
    }
    return nooforgsusers;
  }

  public List<MetricsCountResponse> getOrgWiseUserStats(String monthName) {

    List<MetricsCountResponse> metricsCountResponses = new ArrayList<>();
    var orgsDataByMonth =
        organizationRepository.getOrgWiseUserStatsByMonth(
            Month.valueOf(monthName.trim()).getValue(), findYearByMonth(monthName));
    orgsDataByMonth.forEach(
        result ->
            metricsCountResponses.add(
                MetricsCountResponse.builder()
                    .orgSlug(result.getOrgSlug())
                    .name(result.getOrgName())
                    .count(result.getCount())
                    .date(DateTimeUtil.getEpochFromStringDate(result.getDate()))
                    .build()));

    return metricsCountResponses;
  }
}
