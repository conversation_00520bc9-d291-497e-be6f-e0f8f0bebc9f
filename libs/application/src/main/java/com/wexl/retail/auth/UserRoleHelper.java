package com.wexl.retail.auth;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.globalprofile.service.GlobalProfileService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.staff.model.Staff;
import com.wexl.retail.staff.repository.StaffRepository;
import com.wexl.retail.user.dto.PermissionsResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserRoleHelper implements InitializingBean {

  private static UserRoleHelper instance;
  private final StudentRepository studentRepository;
  private final TeacherRepository teacherRepository;
  private final GlobalProfileService globalProfileService;
  private final OrganizationRepository organizationRepository;
  private final RoleTemplateRepository roleTemplateRepository;
  private final StaffRepository staffRepository;
  private static final String ERROR_INVALIDROLE_TEMPLATE = "error.invalidRoleTemplate";

  @Value("${app.globalProfileMigrationUser}")
  private String specialAdminForGlobalProfile;

  private List<UserRole> getUserRoleFromUser(RoleTemplate roleTemplate) {

    if (AppTemplate.STUDENT.equals(roleTemplate.getTemplate())) {
      return List.of(UserRole.ROLE_ISTUDENT);
    }

    if (AppTemplate.TEACHER.equals(roleTemplate.getTemplate())) {
      return List.of(UserRole.ROLE_ITEACHER);
    }

    if (AppTemplate.ADMIN.equals(roleTemplate.getTemplate())) {
      return List.of(UserRole.ROLE_ITEACHER, UserRole.ROLE_ORG_ADMIN);
    }
    if (AppTemplate.INFIRMARY.equals(roleTemplate.getTemplate())
        || AppTemplate.FINANCE.equals(roleTemplate.getTemplate())
        || AppTemplate.COORDINATOR.equals(roleTemplate.getTemplate())) {
      return List.of(UserRole.ROLE_STAFF);
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_INVALIDROLE_TEMPLATE);
  }

  public List<UserRole> getUserRolesFromUser(User user) {
    var staff = getRoleTemplatesForStaff(user);
    if (staff.isEmpty()) {
      final RoleTemplate roleTemplate = getRoleTemplateFromUser(user);
      return getUserRoleFromUser(roleTemplate);
    }
    return staff.stream().map(this::getUserRoleFromUser).flatMap(List::stream).toList();
  }

  public RoleTemplate getRoleTemplateFromUser(User user) {
    List<AppTemplate> appTemplates =
        List.of(AppTemplate.FINANCE, AppTemplate.INFIRMARY, AppTemplate.COORDINATOR);
    return studentRepository
        .findByUserInfo(user)
        .map(Student::getRoleTemplate)
        .or(() -> teacherRepository.findByUserInfo(user).map(Teacher::getRoleTemplate))
        .or(
            () ->
                staffRepository
                    .findByUser(user)
                    .flatMap(
                        staff ->
                            staff.getRole().stream()
                                .filter(role -> appTemplates.contains(role.getTemplate()))
                                .findFirst()))
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_INVALIDROLE_TEMPLATE));
  }

  private List<RoleTemplate> getRoleTemplatesForStaff(User user) {
    final Optional<Staff> possibleStaff = staffRepository.findByUser(user);
    if (possibleStaff.isPresent()) {
      return possibleStaff.get().getRole();
    }
    return List.of();
  }

  public RoleTemplate findRoleTemplateByOrgSlug(String orgSlug, UserRole userRole) {
    var org = organizationRepository.findBySlug(orgSlug);
    var globalProfile = globalProfileService.getGlobalProfile(org.getProfile().getId());
    var roleTemplates = roleTemplateRepository.findAllByGlobalProfile(globalProfile);

    return getRoleTemplateFromUserRole(userRole, roleTemplates);
  }

  public List<String> getPermissionsForUser(User user) {
    if (isSpecialUser(user)) {
      return List.of(
          "3D_VIDEOS",
          "ACTIVITY_FEED",
          "ACTIVITY_VIDEOS",
          "ADD_CHAPTER_SUBTOPIC",
          "ADMIN",
          "ADMINISTRATION",
          "ORGS_ADMINISTRATION",
          "ALL_ORGS_MLP",
          "ANALYTICS",
          "APP",
          "APP_CENTER",
          "ARVR",
          "ATTENDANCE",
          "QUESTION_BULK_IMPORT",
          "CLASSROOM_ADMIN",
          "CONCEPT_VIDEOS",
          "CONCEPT_VIDEOS_HINDI_MEDIUM",
          "CONFIG_ELP",
          "CREATE_CONCEPT_VIDEOS",
          "DELETE_CONCEPT_VIDEOS",
          "EDIT_ASSETS",
          "GLOBAL_PROFILE",
          "INITIALIZE_ELP",
          "ERP_ATTENDANCE",
          "INSTITUTE_ONBOARD",
          "KNOWLEDGEMETER",
          "LEADER_BOARD",
          "LIVE_CLASSES",
          "LIVE_WORKSHEETS",
          "MANAGE_ASSETS",
          "MANAGE_CHAPTER",
          "MANAGE_COURSES",
          "MANAGE_FEATURES",
          "MANAGE_IMPORTS",
          "MANAGE_ORG_QUESTIONS",
          "MANAGE_PERMISSIONS",
          "MANAGE_PROGRAM",
          "MANAGE_QUESTIONS",
          "MANAGE_SCHEDULED_TESTS",
          "MANAGE_SECTIONS",
          "MANAGE_STUDENTS",
          "MANAGE_SUBJECTS",
          "MANAGE_TEACHERS",
          "MLP_CREATE",
          "MLP_STUDENT_VIEW",
          "MY_CLASSES",
          "MY_TEST_REPORTS",
          "NEW_POST",
          "ORGVIDEOS",
          "PAGE_MAKER",
          "PEPKIDZ_LEARNMATE",
          "PMP_DETAILED_SUMMARY",
          "QUESTION_BANK",
          "SCHEDULE_TEST",
          "SCHOOL_TEST_DROPDOWN",
          "SIMULATION",
          "SPECIAL_EXAM",
          "STUDENT_PROMOTION",
          "STUDENT_SUBJECT_PROFILE",
          "TEST",
          "TEST_REPOSITORY_LIST",
          "TEST_SCHEDULE_CREATE",
          "UPLOAD_ANSWERS_OMR",
          "WEXLDASHBOARD",
          "WEXL_ORG_EXAM_SCHEDULE",
          "WHY_VIDEOS");
    }
    List<RoleTemplate> staffRoleTemplates = getRoleTemplatesForStaff(user);
    if (!staffRoleTemplates.isEmpty()) {
      return staffRoleTemplates.stream()
          .flatMap(template -> template.getRoleTemplateFeatures().stream())
          .map(x -> x.getGlobalFeature().getSlug())
          .distinct()
          .toList();
    }
    try {
      RoleTemplate roleTemplate = getRoleTemplateFromUser(user);
      return roleTemplate.getRoleTemplateFeatures().stream()
          .map(x -> x.getGlobalFeature().getSlug())
          .distinct()
          .toList();
    } catch (ApiException e) {
      return List.of();
    }
  }

  private boolean isSpecialUser(User user) {
    return specialAdminForGlobalProfile.equals(user.getAuthUserId());
  }

  private RoleTemplate getRoleTemplateFromUserRole(
      UserRole userRole, List<RoleTemplate> roleTemplates) {
    var role =
        switch (userRole.name()) {
          case "ROLE_ORG_ADMIN" -> AppTemplate.ADMIN.toString();
          case "ROLE_ISTUDENT" -> AppTemplate.STUDENT.toString();
          case "ROLE_ITEACHER" -> AppTemplate.TEACHER.toString();
          case "ROLE_STAFF" -> AppTemplate.INFIRMARY.toString();
          default -> null;
        };

    var roleTemplate =
        roleTemplates.stream().filter(x -> x.getTemplate().name().equals(role)).findFirst();
    if (roleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_INVALIDROLE_TEMPLATE);
    }
    return roleTemplate.get();
  }

  public boolean isTeacher(User user) {
    return getUserRolesFromUser(user).contains(UserRole.ROLE_ITEACHER);
  }

  public boolean isStudent(User user) {
    return getUserRolesFromUser(user).contains(UserRole.ROLE_ISTUDENT);
  }

  public boolean isOrgAdmin(User user) {
    return getUserRolesFromUser(user).contains(UserRole.ROLE_ORG_ADMIN);
  }

  public boolean isStaff(User user) {
    return getUserRolesFromUser(user).contains(UserRole.ROLE_STAFF);
  }

  public boolean isManager(User user) {
    final RoleTemplate roleTemplate = getRoleTemplateFromUser(user);
    if ("Manager".equals(roleTemplate.getName())
        && AppTemplate.TEACHER.equals(roleTemplate.getTemplate())) {
      return true;
    }
    return false;
  }

  @Override
  public void afterPropertiesSet() {
    instance = this;
  }

  public static UserRoleHelper get() {
    return instance;
  }

  public List<PermissionsResponse> getPermissionsByOrgAndRole(String orgSlug, UserRole userRole) {
    RoleTemplate roleTemplate = findRoleTemplateByOrgSlug(orgSlug, userRole);
    List<PermissionsResponse> responseList = new ArrayList<>();

    roleTemplate
        .getRoleTemplateFeatures()
        .forEach(
            feature -> {
              var globalFeature = feature.getGlobalFeature();
              responseList.add(
                  PermissionsResponse.builder()
                      .name(globalFeature.getName())
                      .slug(globalFeature.getSlug())
                      .description(globalFeature.getDescription())
                      .build());
            });

    return responseList;
  }
}
