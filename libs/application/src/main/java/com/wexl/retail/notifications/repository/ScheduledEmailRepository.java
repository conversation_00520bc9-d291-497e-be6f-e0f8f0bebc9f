package com.wexl.retail.notifications.repository;

import com.wexl.retail.notifications.model.ScheduledEmail;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduledEmailRepository extends JpaRepository<ScheduledEmail, Long> {

  List<ScheduledEmail> findBySentFalseAndScheduledTimeBefore(LocalDateTime now);
}
