package com.wexl.retail.syllabustracking.publisher;

import com.wexl.retail.syllabustracking.model.SyllabusTracking;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class SyllabusTrackingEventPublisher {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishExamCompletion(final SyllabusTracking syllabusTracking) {
    SyllabusTrackingEvent syllabusTrackingEvent = new SyllabusTrackingEvent(syllabusTracking);
    applicationEventPublisher.publishEvent(syllabusTrackingEvent);
  }
}
