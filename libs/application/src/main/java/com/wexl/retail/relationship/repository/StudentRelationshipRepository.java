package com.wexl.retail.relationship.repository;

import com.wexl.retail.model.Student;
import com.wexl.retail.relationship.StudentRelationshipModel;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentRelationshipRepository
    extends JpaRepository<StudentRelationshipModel, Long> {
  List<StudentRelationshipModel> findBystudentA(Student student);

  List<StudentRelationshipModel> findBystudentB(Student student);
}
