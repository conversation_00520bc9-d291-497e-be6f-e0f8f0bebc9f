package com.wexl.retail.globalprofile.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "role_templates")
public class RoleTemplate extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String name;

  private String slug; // should be auto-generated

  @Enumerated(EnumType.STRING)
  private AppTemplate template;

  @ManyToOne(fetch = FetchType.LAZY)
  private GlobalProfile globalProfile;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private List<RoleTemplateFeature> roleTemplateFeatures;
}
