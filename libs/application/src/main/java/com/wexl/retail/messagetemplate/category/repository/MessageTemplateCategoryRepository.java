package com.wexl.retail.messagetemplate.category.repository;

import com.wexl.retail.messagetemplate.category.dto.CategoryType;
import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageTemplateCategoryRepository
    extends JpaRepository<MessageTemplateCategory, Long> {
  List<MessageTemplateCategory> findAllByOrgSlug(String orgSlug);

  List<MessageTemplateCategory> findAllByOrgSlugAndType(String orgSlug, CategoryType categoryType);

  Optional<MessageTemplateCategory> findByOrgSlugAndId(String orgSlug, Long templateId);

  Optional<MessageTemplateCategory> findByOrgSlugAndCategory(String orgSlug, String categoryName);
}
