package com.wexl.retail.globalprofile.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.RoleTemplateFeatureResponse;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.RoleTemplateRequest;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.RoleTemplateResponse;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.GlobalFeature;
import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.globalprofile.model.RoleTemplateFeature;
import com.wexl.retail.globalprofile.repository.GlobalFeatureRepository;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import jakarta.transaction.Transactional;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RoleTemplateService {

  private final GlobalProfileService globalProfileService;
  private final RoleTemplateRepository roleTemplateRepository;
  private final GlobalFeatureRepository globalFeatureRepository;
  private final StudentRepository studentRepository;
  private final TeacherRepository teacherRepository;
  final List<String> roleTemplates = Arrays.asList("ADMIN", "TEACHER", "STUDENT");

  public void saveRoleTemplate(RoleTemplateRequest roleTemplateRequest, Long profileId) {
    var globalProfile = globalProfileService.getGlobalProfile(profileId);
    var slug =
        roleTemplateRequest.name().trim().toLowerCase()
            + "-"
            + globalProfile.getName().trim().toLowerCase();
    var existingRoleTemplate = roleTemplateRepository.findBySlug(slug);
    if (!existingRoleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.RoleTemplate");
    }
    final RoleTemplate roleTemplate =
        RoleTemplate.builder()
            .name(roleTemplateRequest.name())
            .template(roleTemplateRequest.template())
            .globalProfile(globalProfile)
            .slug(slug)
            .build();

    roleTemplate.setRoleTemplateFeatures(
        buildRoleTemplateFeatures(roleTemplate, roleTemplateRequest.features()));
    roleTemplateRepository.save(roleTemplate);
  }

  private List<RoleTemplateFeature> buildRoleTemplateFeatures(
      RoleTemplate roleTemplate, List<Long> globalFeatureIds) {
    final List<GlobalFeature> globalFeatures = buildGlobalFeatures(globalFeatureIds);
    var roleTemplateFeatures =
        globalFeatures.stream()
            .map(
                globalFeature ->
                    RoleTemplateFeature.builder()
                        .roleTemplate(roleTemplate)
                        .globalFeature(globalFeature)
                        .build())
            .toList();
    return new ArrayList<>(roleTemplateFeatures);
  }

  private List<GlobalFeature> buildGlobalFeatures(List<Long> globalFeatureIds) {
    return globalFeatureRepository.findAllByIdIn(globalFeatureIds);
  }

  public List<RoleTemplateResponse> getAllRoleTemplatesForProfile(Long profileId) {
    var globalProfile = globalProfileService.getGlobalProfile(profileId);
    var roleTemplate = roleTemplateRepository.findAllByGlobalProfile(globalProfile);
    if (roleTemplate.isEmpty()) {
      return new ArrayList<>();
    }
    List<RoleTemplateResponse> responses = new ArrayList<>();
    roleTemplate.forEach(
        role ->
            responses.add(
                RoleTemplateResponse.builder()
                    .template(role.getTemplate())
                    .id(role.getId())
                    .name(role.getName())
                    .slug(role.getSlug())
                    .build()));
    return responses;
  }

  public RoleTemplate getRoleTemplateById(Long id) {
    Optional<RoleTemplate> roleTemplate = roleTemplateRepository.findById(id);
    if (roleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    return roleTemplate.get();
  }

  public RoleTemplateResponse getRoleTemplate(Long roleTemplateId) {
    var roleTemplate = getRoleTemplateById(roleTemplateId);
    return buildRoleTemplateResponse(roleTemplate);
  }

  private RoleTemplateResponse buildRoleTemplateResponse(RoleTemplate roleTemplate) {
    return RoleTemplateResponse.builder()
        .id(roleTemplate.getId())
        .name(roleTemplate.getName())
        .slug(roleTemplate.getSlug())
        .template(roleTemplate.getTemplate())
        .features(buildFeaturesResponse(roleTemplate.getRoleTemplateFeatures()))
        .build();
  }

  private List<RoleTemplateFeatureResponse> buildFeaturesResponse(
      List<RoleTemplateFeature> roleTemplateFeatures) {
    List<RoleTemplateFeatureResponse> responseList = new ArrayList<>();
    roleTemplateFeatures.forEach(
        feature ->
            responseList.add(
                RoleTemplateFeatureResponse.builder()
                    .id(feature.getGlobalFeature().getId())
                    .roleTemplate(feature.getRoleTemplate().getName())
                    .name(feature.getGlobalFeature().getName())
                    .description(feature.getGlobalFeature().getDescription())
                    .build()));

    return responseList;
  }

  //  public void deleteById(Long roleTemplateId) {
  //    var roleTemplate = getRoleTemplateById(roleTemplateId);
  //    roleTemplateRepository.deleteById(roleTemplate.getId());
  //  }

  public void updateRoleTemplate(RoleTemplateRequest roleTemplateRequest, Long roleTemplateId) {
    var roleTemplate = getRoleTemplateById(roleTemplateId);
    roleTemplate.setName(roleTemplateRequest.name());
    roleTemplate.setRoleTemplateFeatures(
        buildRoleTemplateFeatures(roleTemplate, roleTemplateRequest.features()));
    roleTemplateRepository.save(roleTemplate);
  }

  @Transactional
  public void updateProfileTemplates(GlobalProfile globalProfile, Organization organization) {
    var roleTemplate = validateRoleTemplates(globalProfile);
    updateAllStudentTemplatesIds(organization, roleTemplate);
    updateTeacherTemplatesIdsByRole(organization, roleTemplate, AppTemplate.TEACHER);
    updateTeacherTemplatesIdsByRole(organization, roleTemplate, AppTemplate.ADMIN);
  }

  private List<RoleTemplate> validateRoleTemplates(GlobalProfile globalProfile) {
    var roleTemplate = roleTemplateRepository.findAllByGlobalProfile(globalProfile);
    if (roleTemplate.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.RoleTemplates.NotFound",
          new String[] {globalProfile.getName()});
    }

    List<String> missingValues =
        roleTemplates.stream()
            .filter(
                role -> roleTemplate.stream().noneMatch(r -> r.getTemplate().name().equals(role)))
            .toList();
    if (!missingValues.isEmpty()) {
      String missingValuesString = String.join(", ", missingValues);
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.RoleTemplate.Configure",
          new String[] {missingValuesString, globalProfile.getName()});
    }
    return roleTemplate;
  }

  private void updateAllStudentTemplatesIds(
      Organization organization, List<RoleTemplate> roleTemplate) {
    var students = studentRepository.findByOrgSlug(organization.getSlug());
    if (!students.isEmpty()) {
      var studentTemplateList =
          roleTemplate.stream()
              .filter(x -> x.getTemplate().equals(AppTemplate.STUDENT))
              .findFirst()
              .orElse(null);
      students.forEach(student -> student.setRoleTemplate(studentTemplateList));
      studentRepository.saveAll(students);
    }
  }

  private void updateTeacherTemplatesIdsByRole(
      Organization organization, List<RoleTemplate> roleTemplate, AppTemplate appTemplate) {
    var teachers =
        teacherRepository.getAllTeacherByOrgSlug(organization.getSlug(), appTemplate.toString());
    if (!teachers.isEmpty()) {
      var teacherRoleTemplate =
          roleTemplate.stream()
              .filter(x -> x.getTemplate().equals(appTemplate))
              .findFirst()
              .orElse(null);
      teachers.forEach(teacher -> teacher.setRoleTemplate(teacherRoleTemplate));
      teacherRepository.saveAll(teachers);
    }
  }

  public RoleTemplateResponse getRoleTemplates(RoleTemplate roleTemplate) {
    return RoleTemplateResponse.builder()
        .id(roleTemplate.getId())
        .name(roleTemplate.getName())
        .slug(roleTemplate.getSlug())
        .template(roleTemplate.getTemplate())
        .build();
  }
}
