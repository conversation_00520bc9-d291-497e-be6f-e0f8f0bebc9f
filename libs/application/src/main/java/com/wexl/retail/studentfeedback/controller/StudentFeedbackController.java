package com.wexl.retail.studentfeedback.controller;

import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.studentfeedback.dto.StudentFeedbackRequest;
import com.wexl.retail.studentfeedback.dto.StudentFeedbackResponse;
import com.wexl.retail.studentfeedback.service.StudentFeedbackService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Valid
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class StudentFeedbackController {

  private final StudentFeedbackService studentFeedbackService;

  @PostMapping("/students/{studentAuthUserId}/feedback")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void studentFeedback(
      @PathVariable("studentAuthUserId") String studentAuthId,
      @PathVariable String orgSlug,
      @RequestBody StudentFeedbackRequest studentFeedbackRequest) {
    studentFeedbackService.createStudentFeedback(studentAuthId, orgSlug, studentFeedbackRequest);
  }

  @IsTeacher
  @GetMapping("/students/{studentAuthUserId}/feedback")
  public List<StudentFeedbackResponse> getFeedbackResponse(
      @PathVariable("studentAuthUserId") String studentAuthId,
      @RequestParam Long fromDateInEpoch,
      @RequestParam Long toDateInEpoch) {
    return studentFeedbackService.getTeacherFeedbackResponse(
        studentAuthId, fromDateInEpoch, toDateInEpoch);
  }

  @PutMapping("/students/{studentAuthUserId}/feedback/{id}")
  public void editFeedback(
      @PathVariable("id") Long feedbackId,
      @PathVariable("studentAuthUserId") String studentAuthId,
      @RequestBody StudentFeedbackRequest studentFeedbackRequest) {
    studentFeedbackService.editFeedback(feedbackId, studentAuthId, studentFeedbackRequest);
  }
}
