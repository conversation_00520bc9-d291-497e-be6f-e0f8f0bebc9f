package com.wexl.retail.organization.admin.teacher;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.model.UserRole;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TeacherResponse {
  private String authId;
  private String userName;
  private long teacherId;
  private String email;
  private String firstName;
  private String lastName;
  private String mobileNumber;
  private boolean orgAdmin;
  private List<String> subjects;
  private List<String> subjectPreferences;
  private List<UserRole> userRoles;
  private long totalTestCreated;
  private long totalMlpCreated;
  private List<String> board;
  private List<String> grade;
  private List<String> section;
  private TeacherDto.CountryResponse countryResponse;
  private String organization;
  private Long lastLoginTime;
  private String status;

  @JsonProperty("role_template")
  private GlobalProfileDto.RoleTemplateResponse roleTemplates;

  private List<TeacherDto.Subject> subjectList;
}
