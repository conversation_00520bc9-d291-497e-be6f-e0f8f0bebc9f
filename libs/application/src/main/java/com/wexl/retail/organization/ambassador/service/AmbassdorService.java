package com.wexl.retail.organization.ambassador.service;

import com.wexl.retail.commons.core.TokenGenerator;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.globalprofile.repository.GlobalProfileRepository;
import com.wexl.retail.model.UiConfig;
import com.wexl.retail.organization.ambassador.model.Ambassador;
import com.wexl.retail.organization.ambassador.model.DiscountDto;
import com.wexl.retail.organization.ambassador.repository.AmbassadorRepository;
import com.wexl.retail.organization.auth.OrgCurriculumRequest;
import com.wexl.retail.organization.auth.OrganizationAuthService;
import com.wexl.retail.organization.auth.OrganizationSignupRequest;
import com.wexl.retail.organization.dto.OrganizationDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.util.ReCaptchaService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
public class AmbassdorService {

  private final OrganizationAuthService organizationAuthService;
  private final GlobalProfileRepository globalProfileRepository;
  private final AmbassadorRepository ambassadorRepository;
  private final ElpService elpService;
  private final ReCaptchaService reCaptchaService;
  private final EmailService emailService;
  private final TokenGenerator tokenGenerator;
  private final SectionService sectionService;
  private final TaskRepository taskRepository;
  private static final String BEARER = "Bearer ";

  List<String> defaultGrades = List.of("i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x");
  String defaultTheme = "theme-wexl.css";
  String defaultLogo =
      "https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/wexl_logo_5df21fe504.png";
  String defaultWebSite = "https://gitanjali.wexledu.com/#/login";

  @Value("${app.ecommerce.elpStoreUrl:https://www.wexledu.com/elp-bset.html}")
  public String bSetStoreUrl;

  @Value("${app.ecommerce.cbseStoreUrl:https://www.wexledu.com/elp-cbse.html}")
  public String cbseStoreUrl;

  String defaultStoreUrl = "https://www.wexledu.com/elp-description.html";
  String defaultLogoutUrl = "https://gitanjali.wexledu.com/#/login";
  String defaultMobileLogo =
      "https://images.wexledu.com/live-worksheets/wexl-internal/20240119114409853.svg";
  String defaultStudentUrl = "https://gitanjali.wexledu.com/student/";

  @Value(
      "${app.ecommerce.service.url:http://product-service.dev.svc.cluster.local:8080/products/%s/%s}")
  public String productServiceUrl;

  private final RestTemplate restTemplate;

  @Transactional
  public void registerAmbassadors(OrganizationDto.SignupRequest request) {
    organizationAuthService.validateEmail(request.email());
    if (!reCaptchaService.verify(request.captchaCode())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReCaptcha.Invalid");
    }
    var organizationSignupRequest = buildOrganizationSignupRequest(request);
    var response =
        organizationAuthService.createOrgInContentAndRetail(
            organizationSignupRequest, Boolean.TRUE);
    var ambassadorEntity = buildAmbassadorEntity(response, request);
    ambassadorRepository.save(ambassadorEntity);
    sectionService.createSectionsForAmbassdor(response.organization().getSlug());
    elpService.initializeElp(response.organization().getSlug(), request.board());
    updateElpTasksDueDate(response.organization().getSlug());
    emailService.sendAmbassadorSignupEmailWithAttachment(
        ambassadorEntity, request, getStoreUrl(request.board()));
  }

  private void updateElpTasksDueDate(String orgSlug) {
    var tasks = taskRepository.findAllByOrgSlugAndTaskTypeAndDueDateNull(orgSlug, TaskType.ELP);
    tasks.forEach(task -> task.setDueDate(LocalDateTime.now()));
    taskRepository.saveAll(tasks);
  }

  private String getStoreUrl(String board) {
    return board.equals("cbse-elp") ? cbseStoreUrl : bSetStoreUrl;
  }

  private Ambassador buildAmbassadorEntity(
      OrganizationDto.SignupResponse response, OrganizationDto.SignupRequest request) {
    Ambassador ambassador = new Ambassador();
    var orgSlug = response.organization().getSlug();
    ambassador.setCode(getDiscountCode(orgSlug));
    ambassador.setEmail(response.user().getEmail());
    ambassador.setName(response.organization().getName());
    ambassador.setOrgSlug(orgSlug);
    ambassador.setMobileNumber(response.user().getMobileNumber());
    ambassador.setReferredBy(request.referredBy());
    return ambassador;
  }

  private String getDiscountCode(String orgSlug) {
    String endPoint = productServiceUrl + "orgs/" + orgSlug + "/discounts:elp";
    var bearerToken = BEARER + tokenGenerator.generateAdminToken();
    try {
      var data =
          restTemplate
              .exchange(
                  endPoint,
                  HttpMethod.GET,
                  getRequestEntity(bearerToken),
                  DiscountDto.DiscountResponse.class)
              .getBody();

      if (data != null && data.discountCode() != null) {
        return data.discountCode();
      }
    } catch (Exception ex) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.DiscountCode");
    }
    return null;
  }

  private HttpEntity<String> getRequestEntity(String bearerToken) {
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    return new HttpEntity<>(null, headers);
  }

  private OrganizationSignupRequest buildOrganizationSignupRequest(
      OrganizationDto.SignupRequest request) {
    var globalProfile = globalProfileRepository.findByName("Elp Profile");
    if (globalProfile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.GlobalProfile");
    }
    return OrganizationSignupRequest.builder()
        .email(request.email())
        .organizationName(request.name().concat(" Institute"))
        .mobileNumber(request.mobileNumber())
        .boards(buildOrgCurriculumRequest(request))
        .isSelfSignup(Boolean.TRUE)
        .organizationBoard(request.board())
        .uiConfig(buildUiConfig(request))
        .profileId(globalProfile.getFirst().getId())
        .password(request.password())
        .isParent(Boolean.TRUE)
        .build();
  }

  private UiConfig buildUiConfig(OrganizationDto.SignupRequest request) {
    return UiConfig.builder()
        .instituteName(request.name() + "institute")
        .theme(defaultTheme)
        .logo(defaultLogo)
        .logoutUrl(defaultLogoutUrl)
        .mobileLogo(defaultMobileLogo)
        .studentUrl(defaultStudentUrl)
        .website(defaultWebSite)
        .storeUrl(defaultStoreUrl)
        .build();
  }

  private List<OrgCurriculumRequest> buildOrgCurriculumRequest(
      OrganizationDto.SignupRequest request) {
    List<OrgCurriculumRequest> list = new ArrayList<>();
    defaultGrades.forEach(
        grade ->
            list.add(
                OrgCurriculumRequest.builder()
                    .boardSlug(request.board())
                    .gradeSlug(grade)
                    .subjectSlug("english")
                    .build()));

    return list;
  }
}
