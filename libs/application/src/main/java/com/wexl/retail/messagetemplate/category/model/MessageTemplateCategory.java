package com.wexl.retail.messagetemplate.category.model;

import com.wexl.retail.messagetemplate.category.dto.CategoryType;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "message_templates_category", schema = "public")
public class MessageTemplateCategory extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private CategoryType type;
  private String category;

  @Column(name = "org_slug")
  private String orgSlug;
}
