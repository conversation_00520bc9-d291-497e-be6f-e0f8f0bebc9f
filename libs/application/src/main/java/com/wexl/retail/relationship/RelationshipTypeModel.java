package com.wexl.retail.relationship;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "relationship_type", schema = "public")
public class RelationshipTypeModel extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "a_isto_b")
  private String aIsToB;

  @Column(name = "b_isto_a")
  private String bIsToA;

  @Column(name = "seq_no")
  private Long seqNo;

  @Column(name = "is_primary")
  private Boolean isPrimary;
}
