package com.wexl.retail.globalprofile.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(
    name = "global_features",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"slug", "template"})})
public class GlobalFeature extends Model {

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "global_features-sequence-generator")
  @SequenceGenerator(
      name = "global_features-sequence-generator",
      sequenceName = "global_features_seq",
      allocationSize = 1)
  private long id;

  private String name;

  @Column(name = "description", columnDefinition = "TEXT")
  private String description;

  private String slug;

  @Enumerated(EnumType.STRING)
  private AppTemplate template;
}
