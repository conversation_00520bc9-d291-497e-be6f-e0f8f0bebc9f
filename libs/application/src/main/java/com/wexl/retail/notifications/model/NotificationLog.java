package com.wexl.retail.notifications.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "notification_logs")
public class NotificationLog extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "notification_id")
  private Long notificationId;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "mobile_number")
  private Long mobileNumber;

  private Boolean rejected;

  @Column(name = "sms_status")
  private Boolean smsStatus;

  @Column(name = "whatsapp_status")
  private Boolean whatsAppStatus;

  @Column(name = "email_status")
  private Boolean emailStatus;

  @Column(name = "rejection_reason")
  private String rejectionReason;
}
