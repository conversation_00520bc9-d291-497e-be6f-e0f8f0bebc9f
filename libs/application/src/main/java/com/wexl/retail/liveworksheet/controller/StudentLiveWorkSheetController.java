package com.wexl.retail.liveworksheet.controller;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto;
import com.wexl.retail.liveworksheet.service.LiveWorkSheetService;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.student.exam.school.SchoolExamService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping(path = "/orgs/{orgSlug}/students/{studentAuthId}")
public class StudentLiveWorkSheetController {

  private final LiveWorkSheetService liveWorkSheetService;
  private final SchoolExamService schoolExamService;

  @IsStudent
  @GetMapping("/live-worksheets")
  public List<LiveWorkSheetDto.LiveWorkSheetResponse> getStudentLiveWorkSheet(
      @PathVariable String orgSlug,
      @RequestParam(name = "isScheduled", defaultValue = "true") boolean isScheduledTest) {
    return liveWorkSheetService.getStudentLiveWorkSheets(orgSlug, isScheduledTest);
  }

  @IsStudent
  @GetMapping("/live-worksheets/{testDefinitionId}")
  public LiveWorkSheetDto.LiveWorksheetResult getLiveWorksheetResponse(
      @PathVariable String orgSlug, @PathVariable long testDefinitionId) {
    return liveWorkSheetService.getLiveWorksheetResponse(orgSlug, testDefinitionId);
  }

  @GetMapping("/live-worksheets/{testDefinitionId}:exam")
  public ExamResponse createLiveWorksheetExam(
      @PathVariable String orgSlug, @PathVariable("testDefinitionId") long id) {
    return schoolExamService.createLiveWorksheetExam(orgSlug, id);
  }
}
