package com.wexl.retail.syllabustracking.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.syllabustracking.dto.SyllabusDto;
import com.wexl.retail.syllabustracking.service.SyllabusTrackingService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/orgs/{orgSlug}")
public class SyllabusTrackingController {

  private final SyllabusTrackingService syllabusTrackingService;

  @IsOrgAdmin
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/syllabus-trackings")
  public void initiateSyllabusTracking(@PathVariable("orgSlug") String orgSlug) {
    syllabusTrackingService.initiateSyllabusTracking(orgSlug);
  }

  @IsTeacher
  @GetMapping("/sections/{sectionUuid}/subjects/{subjectSlug}/syllabus-trackings")
  public SyllabusDto.SyllabusTrackingResponse getTeacherSyllabus(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("sectionUuid") String sectionUuid,
      @PathVariable("subjectSlug") String subjectSlug,
      @RequestParam("board") String boardSlug) {
    return syllabusTrackingService.getTeacherSyllabus(orgSlug, boardSlug, sectionUuid, subjectSlug);
  }

  @IsTeacher
  @PostMapping("/syllabus-trackings/{id}")
  public void updateSyllabusTrackingStatus(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("id") long id,
      @RequestBody SyllabusDto.SyllabusStatusRequest requests) {
    syllabusTrackingService.updateSyllabusTrackingStatus(orgSlug, id, requests);
  }

  @IsTeacher
  @GetMapping("/chapters-count")
  public List<SyllabusDto.ChapterCounts> chaptersCount(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam("sectionUuid") List<String> sectionUuid,
      @RequestParam("board") String boardSlug,
      @RequestParam("grade") String gradeSlug) {
    return syllabusTrackingService.getChapterCountDetails(
        orgSlug, sectionUuid, boardSlug, gradeSlug);
  }

  @IsTeacher
  @GetMapping("/subjects/{subjectSlug}/subtopics-count")
  public List<SyllabusDto.subTopicDetails> subTopicCount(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam("sectionUuid") List<String> sectionUuid,
      @RequestParam("board") String boardSlug,
      @RequestParam("grade") String gradeSlug,
      @PathVariable("subjectSlug") String subjectSlug) {
    return syllabusTrackingService.getSubTopicCountDetails(
        orgSlug, sectionUuid, boardSlug, gradeSlug, subjectSlug);
  }
}
