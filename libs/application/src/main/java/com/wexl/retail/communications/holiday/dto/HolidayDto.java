package com.wexl.retail.communications.holiday.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.notifications.dto.NotificationDto;
import java.util.List;
import lombok.Builder;

public record HolidayDto() {

  public record HolidayRequest(
      String title,
      String message,
      @JsonProperty("grade_slugs") List<String> gradeSlugs,
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate,
      List<String> attachment,
      List<String> link,
      Boolean isAllGrades) {

    public HolidayRequest {
      if (isAllGrades == null) {
        isAllGrades = false;
      }
    }
  }

  @Builder
  public record HolidayResponse(
      List<NotificationDto.TeacherNotificationResponse> holidayResponse) {}

  @Builder
  public record StudentHolidayResponse(
      List<NotificationDto.TeacherNotificationResponse> holidayResponse) {}
}
