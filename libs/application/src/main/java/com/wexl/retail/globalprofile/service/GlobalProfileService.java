package com.wexl.retail.globalprofile.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalProfileRequest;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto.GlobalProfileResponse;
import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.globalprofile.repository.GlobalProfileRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class GlobalProfileService {

  private final GlobalProfileRepository globalProfileRepository;
  //  private final GlobalFeatureRepository globalFeatureRepository;
  private final OrganizationRepository organizationRepository;

  public List<GlobalProfileResponse> getAllProfiles() {
    final List<GlobalProfile> globalProfiles = globalProfileRepository.findAll();
    return globalProfiles.stream()
        .map(
            p ->
                GlobalProfileResponse.builder()
                    .id(p.getId())
                    .name(p.getName())
                    .description(p.getDescription())
                    .build())
        .toList();
  }

  public void saveProfile(GlobalProfileRequest globalProfileRequest) {
    var profile = globalProfileRepository.findByName(globalProfileRequest.name());
    if (!profile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.GlobalProfile");
    }
    final GlobalProfile globalProfile =
        GlobalProfile.builder()
            .name(globalProfileRequest.name())
            .description(globalProfileRequest.description())
            .build();
    globalProfileRepository.save(globalProfile);
  }

  public void updateProfile(Long id, GlobalProfileRequest globalProfileRequest) {
    final GlobalProfile globalProfile = getGlobalProfile(id);
    globalProfile.setName(globalProfileRequest.name());
    globalProfile.setDescription(globalProfileRequest.description());
    globalProfileRepository.save(globalProfile);
  }

  //  public void updateProfileFeatures(
  //      Long id, GlobalProfileFeatureRequest globalProfileFeatureRequest) {
  //    final GlobalProfile globalProfile = getGlobalProfile(id);
  //    var existingFeatures = globalProfile.getFeatures();
  //    var ids =
  //        existingFeatures.stream()
  //            .filter(x -> globalProfileFeatureRequest.template().equals(x.getTemplate()))
  //            .map(GlobalFeature::getId)
  //            .toList();
  //    List<Long> globalFeatureIds = new ArrayList<>(ids);
  //    globalFeatureIds.addAll(globalProfileFeatureRequest.ids());
  //    final List<GlobalFeature> globalFeatures =
  //        globalFeatureRepository.findAllById(globalFeatureIds.stream().distinct().toList());
  //    globalProfile.setFeatures(globalFeatures);
  //    globalProfileRepository.save(globalProfile);
  //  }

  public GlobalProfile getGlobalProfile(Long id) {
    final Optional<GlobalProfile> possibleGlobalProfile = globalProfileRepository.findById(id);
    if (possibleGlobalProfile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    return possibleGlobalProfile.get();
  }

  //  public GlobalProfileFeaturesResponse getProfileFeatures(Long id) {
  //    final var globalProfile = getGlobalProfile(id);
  //    return GlobalProfileFeaturesResponse.builder()
  //        .features(new ArrayList<>())
  //        .id(globalProfile.getId())
  //        .name(globalProfile.getName())
  //        .description(globalProfile.getDescription())
  //        .build();
  //  }

  public GlobalProfileResponse getGlobalProfileByOrg(String orgSlug) {
    Organization organization = organizationRepository.findBySlug(orgSlug);
    if (organization.getProfile() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.GlobalProfile");
    }
    var profile = organization.getProfile();
    return GlobalProfileResponse.builder()
        .id(profile.getId())
        .name(profile.getName())
        .description(profile.getDescription())
        .build();
  }
}
