package com.wexl.retail.organization.dashboard;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.dto.ServicesCount;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DashboardService {

  private final UserRepository userRepository;
  private final DateTimeUtil dateTimeUtil;
  private final TestDefinitionRepository testDefinitionRepository;
  private final MlpRepository mlpRepository;
  private final OrganizationRepository organizationRepository;

  LocalDate today = LocalDate.now();
  LocalDate yesterday = today.minusDays(1);
  LocalDate dayBeforeYesterday = today.minusDays(2);

  public ChildOrgs getMyOrgsInfo(String authUserId) {
    User teacherUser = userRepository.getUserByAuthUserId(authUserId);
    Teacher teacher = teacherUser.getTeacherInfo();
    List<Organization> childOrgs = teacher.getChildOrgs();
    List<String> childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();
    List<ChildOrgsInfo> childOrgsInfos = new ArrayList<>();
    for (Organization org : childOrgs) {
      childOrgsInfos.add(
          ChildOrgsInfo.builder()
              .orgName(org.getName())
              .startDate(dateTimeUtil.convertIso8601ToEpoch(org.getCreatedAt().toLocalDateTime()))
              .status(Objects.isNull(org.getDeletedAt()) ? "ACTIVE" : "INACTIVE")
              .orgSlug(org.getSlug())
              .build());
    }

    return ChildOrgs.builder()
        .orgsCount(childOrgs.size())
        .studentsCount(userRepository.getStudentsCountOfOrg(childOrgSlugs))
        .teachersCount(userRepository.getTeachersCountOfOrg(childOrgSlugs))
        .totalMlpCount(mlpRepository.getMlpCount(childOrgSlugs))
        .mlpsCreatedYesterdayCount(
            mlpRepository.getMlpsCreatedYesterday(childOrgSlugs, yesterday.toString()))
        .mlpsCreatedDayBeforeYesterday(
            mlpRepository.getMlpsCreatedYesterday(childOrgSlugs, dayBeforeYesterday.toString()))
        .childOrgsInfos(childOrgsInfos)
        .build();
  }

  public ServicesCount getServicesUsage(String orgSlug, long fromDate, long toDate) {
    var fromTime = fromDate / 1000L;
    var toTime = toDate / 1000L;

    List<String> orgSlugList = new ArrayList<>();
    orgSlugList.add(orgSlug);
    Organization org = organizationRepository.findBySlug(orgSlug);

    return ServicesCount.builder()
        .assignmentsCount(testDefinitionRepository.countByTestType(orgSlug, "ASSIGNMENT"))
        .mlpsCount(mlpRepository.mlpCountByOrg(orgSlug))
        .mlpsCountYesterdayByDate(
            mlpRepository.getMlpsCreatedYesterday(orgSlugList, yesterday.toString()))
        .mlpsCountDayBeforeYesterdayByDate(
            mlpRepository.getMlpsCreatedYesterday(orgSlugList, dayBeforeYesterday.toString()))
        .studentsCount(userRepository.getStudentsCountOfOrg(orgSlugList))
        .testsCount(testDefinitionRepository.noOfTestsByOrg(orgSlug))
        .teachersCount(userRepository.getTeachersCountOfOrg(orgSlugList))
        .worksheetsCount(testDefinitionRepository.countByTestType(orgSlug, "WORKSHEET"))
        .timeSpent((userRepository.countByStudentTime(orgSlug)))
        .mlpStudentActivities(mlpRepository.getMlpStudentActivity(orgSlug, fromTime, toTime))
        .mlpTeacherActivities(mlpRepository.getMlpTeacherActivity(orgSlug, fromTime, toTime))
        .orgSlug(orgSlug)
        .orgName(org.getName())
        .attributes(org.getAttributes())
        .build();
  }
}
