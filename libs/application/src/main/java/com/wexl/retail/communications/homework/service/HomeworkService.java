package com.wexl.retail.communications.homework.service;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.communications.homework.dto.HomeworkDto;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.Notification;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.UserRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HomeworkService {
  private final NotificationsService notificationService;
  private final UserRepository userRepository;
  private final NotificationRepository notificationRepository;
  private final EventNotificationService eventNotificationService;

  public void createHomeworkNotification(
      String orgSlug, HomeworkDto.HomeworkRequest request, String teacherAuthId) {
    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.message())
            .sectionUuids(request.sectionUuids())
            .studentIds(request.studentIds())
            .attachment(request.attachment())
            .link(request.link())
            .notificationType(com.wexl.retail.notifications.model.NotificationType.SECTION)
            .feature(CommunicationFeature.HOMEWORK)
            .build();

    notificationService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);
    eventNotificationService.sendPushNotificationForSection(
        orgSlug,
        notificationRequest.message(),
        notificationRequest.title(),
        notificationRequest.sectionUuids(),
        notificationRequest.studentIds(),
        teacherAuthId);
  }

  public HomeworkDto.HomeworkResponse getHomeworkNotifications(
      String orgSlug, String teacherAuthId) {
    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();
    List<Notification> homeworkNotifications;
    if (UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())) {
      homeworkNotifications =
          notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
              orgSlug, CommunicationFeature.HOMEWORK);
    } else {
      homeworkNotifications =
          notificationRepository.findAllByCreatedByAndOrgSlugAndFeatureOrderByCreatedAtDesc(
              teacher, orgSlug, CommunicationFeature.HOMEWORK);
    }
    var notificationResponse =
        notificationService.buildNotificationResponse(homeworkNotifications, 100);
    return HomeworkDto.HomeworkResponse.builder().homeworkResponse(notificationResponse).build();
  }

  public HomeworkDto.StudentHomeworkResponse getStudentHomeworkNotifications(
      String orgSlug, String studentAuthId) {
    var student =
        userRepository.findByAuthUserIdAndOrganization(studentAuthId, orgSlug).getStudentInfo();
    var studentHomeworkNotifications =
        notificationRepository
            .findByOrgSlugAndFeatureAndStudentNotifications_StudentAndDeletedAtIsNullOrderByCreatedAtDesc(
                orgSlug, CommunicationFeature.HOMEWORK, student);
    var notificationResponse =
        notificationService.getStudentNotificationResponses(
            studentHomeworkNotifications, student.getId());
    return HomeworkDto.StudentHomeworkResponse.builder()
        .homeworkResponse(notificationResponse)
        .build();
  }

  public void updateHomeworkNotification(
      String orgSlug, Long notificationId, HomeworkDto.HomeworkRequest request) {
    var newNotificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.message())
            .sectionUuids(request.sectionUuids())
            .studentIds(request.studentIds())
            .attachment(request.attachment())
            .link(request.link())
            .notificationType(NotificationType.SECTION)
            .build();
    notificationService.editNotificationByTeacher(
        orgSlug, newNotificationRequest, null, notificationId);
  }
}
