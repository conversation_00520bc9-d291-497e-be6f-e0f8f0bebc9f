package com.wexl.retail.studentfeedback.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum FeedbackType {
  ACTIVITY("ACTIVITY"),
  SUBJECT("SUBJECT"),
  SUMMARY("SUMMARY");
  private final String value;

  public static FeedbackType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (FeedbackType enumEntry : FeedbackType.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
