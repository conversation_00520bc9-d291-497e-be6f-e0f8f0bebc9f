package com.wexl.retail.notifications.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.List;
import lombok.*;
import org.hibernate.annotations.Type;

@Data
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = "teacherNotifications")
@Builder
@Entity
@AllArgsConstructor
@Table(name = "notifications")
public class Notification extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @Column(name = "notification_type")
  private NotificationType notificationType;

  @Column(name = "message", columnDefinition = "VARCHAR(5000)")
  private String message;

  @Type(JsonType.class)
  @Column(name = "attachments", columnDefinition = "jsonb")
  private List<String> attachments;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization organization;

  @Column(name = "org_slug")
  private String orgSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher createdBy;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "notification")
  private List<StudentNotification> studentNotifications;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "notification")
  private List<ClassroomNotification> classroomNotifications;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "notification")
  private List<SectionNotification> sectionNotifications;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "notification")
  private List<TeacherNotification> teacherNotifications;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "notification")
  private List<StaffNotification> staffNotifications;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "message_template_id")
  private MessageTemplate messageTemplate;

  @JsonProperty("category_id")
  private Long categoryId;

  @Type(JsonType.class)
  @Column(name = "link", columnDefinition = "jsonb")
  private List<String> link;

  @JsonProperty("from_date")
  private Timestamp fromDate;

  @JsonProperty("to_date")
  private Timestamp toDate;

  @Enumerated(EnumType.STRING)
  private CommunicationFeature feature;
}
