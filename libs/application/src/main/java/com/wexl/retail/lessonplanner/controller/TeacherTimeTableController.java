package com.wexl.retail.lessonplanner.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.lessonplanner.dto.TeacherTimeTableDto;
import com.wexl.retail.lessonplanner.service.TeacherTimeTableService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class TeacherTimeTableController {
  private final TeacherTimeTableService teacherTimeTableService;
  private final AuthService authService;

  @PostMapping("/teachers-timetable")
  public void createTimeTable(
      @PathVariable String orgSlug, @RequestBody TeacherTimeTableDto.TimeTableRequest request) {
    teacherTimeTableService.createTimeTable(orgSlug, request);
  }

  @GetMapping("/teachers-timetable")
  public TeacherTimeTableDto.TimeTableResponse getTimeTable(
      @PathVariable String orgSlug,
      @RequestParam String boardSlug,
      @RequestParam String gradeSlug,
      @RequestParam String sectionUuid) {
    return teacherTimeTableService.getTimeTable(orgSlug, boardSlug, gradeSlug, sectionUuid, false);
  }

  @PutMapping("/teachers-timetable")
  public void updateTimeTable(@RequestBody TeacherTimeTableDto.PeriodsRequestBulk request) {
    teacherTimeTableService.updateTimeTable(request);
  }

  @DeleteMapping("/teachers-timetable/teachers-timetable-details/{teacherTimeTableDetailId}")
  public void deleteTimeTable(@PathVariable Long teacherTimeTableDetailId) {
    teacherTimeTableService.deleteTimeTable(teacherTimeTableDetailId);
  }

  @GetMapping("/teachers-timetable/teachers/{teacherAuthId}")
  public List<TeacherTimeTableDto.DateResponse> getTeacherTimeTable(
      @PathVariable String teacherAuthId,
      @RequestParam(name = "teacherId", required = false) Long teacherId) {
    var user = authService.getUserByAuthUserId(teacherAuthId);
    if (Objects.nonNull(teacherId)) {
      return teacherTimeTableService.getTeacherTimeTable(teacherId);
    }
    return teacherTimeTableService.getTeacherTimeTable(user.getTeacherInfo().getId());
  }

  @GetMapping("/students-timetable/students/{studentAuthId}")
  public TeacherTimeTableDto.TimeTableResponse getStudentTimeTable(
      @PathVariable String orgSlug, @PathVariable String studentAuthId) {
    return teacherTimeTableService.getStudentTimeTable(orgSlug, studentAuthId);
  }

  @PostMapping("/teachers-timetable:clone")
  public void cloneTimeTable(@RequestBody TeacherTimeTableDto.CloneRequest request) {
    teacherTimeTableService.cloneTimeTable(request);
  }

  @GetMapping("/timetable-periods")
  public List<TeacherTimeTableDto.PeriodResponse> TeacherTimeTablePeriods() {
    return teacherTimeTableService.getPeriodResponse();
  }

  @GetMapping("/sections/{sectionUuid}/allTeacherSubjects")
  public List<TeacherTimeTableDto.TeacherSubjectResponse> getTeacherSubjects(
      @PathVariable String orgSlug, @PathVariable String sectionUuid) {
    return teacherTimeTableService.getAllTeachersSubjects(orgSlug, sectionUuid);
  }
}
