package com.wexl.retail.relationship.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.relationship.StudentRelationshipModel;
import com.wexl.retail.relationship.dto.RelationshipDto;
import com.wexl.retail.relationship.repository.RelationshipTypeRepository;
import com.wexl.retail.relationship.repository.StudentRelationshipRepository;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RelationshipService {

  private final ValidationUtils validationUtils;
  private final RelationshipTypeRepository relationshipTypeRepository;
  private final StudentRelationshipRepository studentRelationshipRepository;

  public void saveStudentRelationship(String studentAuthId, RelationshipDto.Request request) {
    var userA = validationUtils.isValidUser(studentAuthId);
    var studentA = userA.getStudentInfo();
    var userB = validationUtils.isValidUser(request.studentAuthId());
    var studentB = userB.getStudentInfo();
    if (!Objects.equals(userA.getOrganization(), userB.getOrganization())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          " error.Relationship.organization",
          new String[] {userA.getOrganization(), userB.getOrganization()});
    }

    var relationshipType = relationshipTypeRepository.findByaIsToB(request.relationshipType());
    studentRelationshipRepository.save(
        StudentRelationshipModel.builder()
            .studentA(studentA)
            .studentB(studentB)
            .relationshipModel(relationshipType)
            .build());
  }

  public List<RelationshipDto.Response> getStudentRelationship(String studentAuthId) {
    var user = validationUtils.isValidUser(studentAuthId);
    var student = user.getStudentInfo();
    List<RelationshipDto.Response> responses = new ArrayList<>();
    responses.addAll(getStudentARelation(student));
    responses.addAll(getStudentBRelation(student));
    return responses.stream().distinct().toList();
  }

  private List<RelationshipDto.Response> getStudentBRelation(Student student) {
    var studentB = studentRelationshipRepository.findBystudentB(student);
    List<RelationshipDto.Response> responses = new ArrayList<>();
    studentB.forEach(
        studentb -> {
          var studentA = studentb.getStudentA();
          var section = studentA.getSection();
          var userA = studentA.getUserInfo();
          responses.add(
              RelationshipDto.Response.builder()
                  .relationshipType(studentb.getRelationshipModel().getBIsToA())
                  .studentAuthId(userA.getAuthUserId())
                  .name(userA.getFirstName() + userA.getLastName())
                  .sectionName(section.getName())
                  .gradeName(section.getGradeName())
                  .gradeSlug(section.getGradeSlug())
                  .build());
        });

    return responses;
  }

  private List<RelationshipDto.Response> getStudentARelation(Student student) {
    var studentA = studentRelationshipRepository.findBystudentA(student);
    List<RelationshipDto.Response> responses = new ArrayList<>();
    studentA.forEach(
        studenta -> {
          var studentB = studenta.getStudentB();
          var section = studentB.getSection();
          var userB = studentB.getUserInfo();
          responses.add(
              RelationshipDto.Response.builder()
                  .relationshipType(studenta.getRelationshipModel().getAIsToB())
                  .studentAuthId(userB.getAuthUserId())
                  .name(userB.getFirstName() + userB.getLastName())
                  .sectionName(section.getName())
                  .gradeName(section.getGradeName())
                  .gradeSlug(section.getGradeSlug())
                  .build());
        });
    return responses;
  }

  public List<RelationshipDto.Types> getRelationshipTypes() {
    var relationshipTypes = relationshipTypeRepository.findAll();

    List<RelationshipDto.Types> response = new ArrayList<>();
    relationshipTypes.forEach(
        relationship ->
            response.add(
                RelationshipDto.Types.builder()
                    .id(relationship.getId())
                    .aIsToB(relationship.getAIsToB())
                    .bIsToA(relationship.getBIsToA())
                    .isPrimary(relationship.getIsPrimary())
                    .seqNo(relationship.getSeqNo())
                    .build()));

    return response;
  }
}
