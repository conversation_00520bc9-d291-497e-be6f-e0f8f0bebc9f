package com.wexl.retail.syllabustracking.repository;

import com.wexl.retail.syllabustracking.dto.ChapterResponse;
import com.wexl.retail.syllabustracking.model.SyllabusTracking;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface SyllabusTrackingRepository extends JpaRepository<SyllabusTracking, Long> {

  Optional<SyllabusTracking> findByIdAndOrgSlugAndSubtopicSlug(
      long id, String orgSlug, String subtopic);

  List<SyllabusTracking>
      findByOrgSlugAndBoardSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNullOrderByIdAsc(
          String orgSlug, String boardSlug, String section, String subject);

  List<SyllabusTracking>
      findAllByOrgSlugAndBoardSlugAndGradeSlugInAndSubjectSlugInAndDeletedAtIsNull(
          String orgSlug, String board, List<String> grades, List<String> subjects);

  List<SyllabusTracking>
      findAllByOrgSlugAndSectionUuidAndBoardSlugAndSubjectSlugInAndDeletedAtIsNull(
          String orgSlug, String sectionUuid, String boardSlug, List<String> subjects);

  List<SyllabusTracking> findAllByOrgSlugAndSectionUuidAndBoardSlugAndDeletedAtIsNull(
      String orgSlug, String sectionUuid, String boardSlug);

  boolean existsByOrgSlug(String orgSlug);

  Set<SyllabusTracking> findAllByOrgSlugAndBoardSlugAndGradeSlugAndDeletedAtIsNull(
      String organization, String boardSlug, String gradeSlug);

  @Query(
      value =
          """
                         SELECT
                              subject_name AS subjectName,subject_slug as subjectSlug,
                              COUNT(DISTINCT chapter_slug) AS totalChapters,
                              COUNT(DISTINCT CASE
                                                 WHEN totalSubtopics = completedSubtopics THEN chapter_slug
                                             END) AS completedChapters,
                                      ARRAY_AGG(
                                           CASE WHEN totalSubtopics = completedSubtopics THEN chapter_name END
                                       ) FILTER (WHERE totalSubtopics = completedSubtopics) AS completedChapterNames,
                                       ARRAY_AGG(
                                           CASE WHEN totalSubtopics = completedSubtopics THEN chapter_slug END
                                       ) FILTER (WHERE totalSubtopics = completedSubtopics) AS completedChapterSlugs,
                              COUNT(DISTINCT CASE
                                                 WHEN totalSubtopics != completedSubtopics THEN chapter_slug
                                             END) AS pendingChapters,
                                  ARRAY_AGG(
                                       CASE WHEN totalSubtopics > completedSubtopics THEN chapter_name END
                                   ) FILTER (WHERE totalSubtopics > completedSubtopics) AS pendingChapterNames,
                                   ARRAY_AGG(
                                       CASE WHEN totalSubtopics > completedSubtopics THEN chapter_slug END
                                   ) FILTER (WHERE totalSubtopics > completedSubtopics) AS pendingChapterSlugs
                          FROM (
                              SELECT
                                  st.subject_name,
                                  st.chapter_slug,
                                  st.chapter_name,
                                  st.subject_slug,
                                  COUNT(DISTINCT st.subtopic_slug) AS totalSubtopics,
                                  SUM(CASE WHEN st.status = 'COMPLETED' THEN 1 ELSE 0 END) AS completedSubtopics
                              FROM
                                  syllabus_trackings st
                              WHERE
                                  st.org_slug = :orgSlug
                                  AND st.board_slug = :boardSlug
                                  AND st.grade_slug = :gradeSlug
                                  AND st.section_uuid = :sectionUuid
                                  AND st.deleted_at IS NULL
                              GROUP BY
                                  st.subject_name, st.chapter_slug, st.chapter_name,st.subject_slug
                          ) chapter_completions
                          GROUP BY
                              subject_name, subject_slug;
                 """,
      nativeQuery = true)
  List<ChapterResponse>
      getChapterCountDetailsByOrgSlugAndSectionUuidAndBoardSlugAndGradeSlugAndDeletedAtIsNull(
          String orgSlug, String sectionUuid, String boardSlug, String gradeSlug);

  @Query(
      value =
          """
                             SELECT
                              st.subtopic_name AS subtopicName,
                              st.subtopic_slug as subtopicSlug,
                              COUNT(DISTINCT st.subtopic_slug) AS totalSubtopics,
                              SUM(CASE WHEN st.status = 'COMPLETED' THEN 1 ELSE 0 END) AS completedSubtopics,
                              COUNT(DISTINCT st.subtopic_slug) - SUM(CASE WHEN st.status = 'COMPLETED' THEN 1 ELSE 0 END) AS pendingSubtopics
                          FROM
                              syllabus_trackings st
                          WHERE
                              st.org_slug = :orgSlug
                              AND st.board_slug = :boardSlug
                              AND st.grade_slug = :gradeSlug
                              AND st.section_uuid = :sectionUuid
                              AND st.subject_slug = :subjectSlug
                              AND st.deleted_at IS NULL
                          GROUP BY
                              st.subtopic_name, st.subtopic_slug
                  """,
      nativeQuery = true)
  List<ChapterResponse>
      getSubtopicCountDetailsByOrgSlugAndSectionUuidAndBoardSlugAndGradeSlugAndSubjectSlugAndDeletedAtIsNull(
          String orgSlug,
          String sectionUuid,
          String boardSlug,
          String gradeSlug,
          String subjectSlug);
}
