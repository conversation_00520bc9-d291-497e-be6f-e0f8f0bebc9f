package com.wexl.retail.messagetemplate.dto;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
public enum MessageType {
  NOTIFICATION("NOTIFICATION"),
  FEEDBACK("FEEDBACK"),
  EMAIL("EMAIL"),
  SMS_WHATSAPP("SMS_WHATSAPP");

  private final String value;

  public static MessageType fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (MessageType enumEntry : MessageType.values()) {
      if (enumEntry.toString().equals(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Cannot create enum from " + value + " value!");
  }

  @Override
  public String toString() {
    return this.value;
  }
}
