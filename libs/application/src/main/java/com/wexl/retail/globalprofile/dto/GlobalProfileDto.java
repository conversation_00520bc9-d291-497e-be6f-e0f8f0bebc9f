package com.wexl.retail.globalprofile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.model.AppTemplate;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record GlobalProfileDto() {
  @Builder
  public record GlobalProfileRequest(@NotNull String name, @NotNull String description) {}

  @Builder
  public record GlobalProfileResponse(Long id, String name, String description) {}

  @Builder
  public record GlobalProfileFeaturesResponse(
      Long id, String name, String description, List<GlobalFeatureResponse> features) {}

  @Builder
  public record GlobalProfileFeatureRequest(List<Long> ids, @NotNull AppTemplate template) {}

  @Builder
  public record GlobalFeatureRequest(
      @NotNull String name,
      @NotNull String description,
      @NotNull String slug,
      @NotNull AppTemplate template) {}

  @Builder
  public record GlobalFeatureResponse(
      Long id,
      String name,
      String description,
      String slug,
      @JsonProperty("role_template") String roleTemplate) {}

  @Builder
  public record RoleTemplateRequest(
      @NotNull String name, @NotNull AppTemplate template, List<Long> features) {}

  @Builder
  public record RoleTemplateResponse(
      Long id,
      @NotNull String name,
      @NotNull String slug,
      @NotNull AppTemplate template,
      List<RoleTemplateFeatureResponse> features) {}

  @Builder
  public record RoleTemplateFeatureResponse(
      Long id,
      String name,
      String description,
      String slug,
      @JsonProperty("role_template") String roleTemplate) {}
}
