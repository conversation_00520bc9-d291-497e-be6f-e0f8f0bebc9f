package com.wexl.retail.messagetemplate.category.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.messagetemplate.category.dto.CategoryType;
import com.wexl.retail.messagetemplate.category.dto.MessageTemplateCategoryDto;
import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.messagetemplate.category.repository.MessageTemplateCategoryRepository;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MessageTemplateCategoryService {
  @Autowired MessageTemplateCategoryRepository messageTemplateCategoryRepository;
  @Autowired MessageTemplateRepository messageTemplateRepository;
  private static final String INVALID_MESSAGE_TEMPLATE_CATEGORY =
      "error.Invalid.MessageTemplateCategory";
  private static final String MESSAGE_TEMPLATE_EXITS = "error.Invalid.MessageTemplateExits";

  public void createMessageTemplateCategory(
      String orgSlug, MessageTemplateCategoryDto.Request request) {
    messageTemplateCategoryRepository.save(buildMessageTemplateRequest(orgSlug, request));
  }

  private MessageTemplateCategory buildMessageTemplateRequest(
      String orgSlug, MessageTemplateCategoryDto.Request request) {
    return MessageTemplateCategory.builder()
        .category(request.category())
        .type(request.type())
        .orgSlug(orgSlug)
        .build();
  }

  public List<MessageTemplateCategoryDto.Response> getMessageTemplatesCategory(
      String orgSlug, Optional<CategoryType> type) {

    if (type.isPresent()) {
      List<MessageTemplateCategory> messageTemplateCategoriesListByType =
          messageTemplateCategoryRepository.findAllByOrgSlugAndType(orgSlug, type.get());
      return buildMessageTemplateCategoryResponse(messageTemplateCategoriesListByType);
    }
    List<MessageTemplateCategory> messageTemplateCategories =
        messageTemplateCategoryRepository.findAllByOrgSlug(orgSlug);
    return buildMessageTemplateCategoryResponse(messageTemplateCategories);
  }

  private List<MessageTemplateCategoryDto.Response> buildMessageTemplateCategoryResponse(
      List<MessageTemplateCategory> messageTemplateCategories) {

    return messageTemplateCategories.stream()
        .map(
            messageTemplateCategory ->
                MessageTemplateCategoryDto.Response.builder()
                    .id(messageTemplateCategory.getId())
                    .category(messageTemplateCategory.getCategory())
                    .type(messageTemplateCategory.getType())
                    .build())
        .toList();
  }

  public void editMessageTemplateCategory(
      String orgSlug, Long templateCategoryId, MessageTemplateCategoryDto.Request request) {
    var messageTemplateCategory =
        messageTemplateCategoryRepository.findByOrgSlugAndId(orgSlug, templateCategoryId);
    if (messageTemplateCategory.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_MESSAGE_TEMPLATE_CATEGORY);
    }
    var messageTemplateCategoryData = messageTemplateCategory.get();
    messageTemplateCategoryData.setCategory(request.category());
    messageTemplateCategoryRepository.save(messageTemplateCategoryData);
  }

  public void deleteMessageTemplateCategory(String orgSlug, Long templateCategoryId) {
    var messageTemplateCategory =
        messageTemplateCategoryRepository.findByOrgSlugAndId(orgSlug, templateCategoryId);
    if (messageTemplateCategory.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_MESSAGE_TEMPLATE_CATEGORY);
    }
    List<MessageTemplate> messageTemplate =
        messageTemplateRepository.findByMessageTemplateCategory(messageTemplateCategory.get());
    if (!messageTemplate.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          MESSAGE_TEMPLATE_EXITS,
          new String[] {messageTemplateCategory.get().getCategory()});
    }
    messageTemplateCategoryRepository.delete(messageTemplateCategory.get());
  }
}
