package com.wexl.retail.liveworksheet.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;
import static com.wexl.retail.commons.util.ExtensionUtil.CONTENT_TYPE_EXTENSIONS;
import static java.lang.String.format;
import static java.time.ZoneOffset.UTC;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ExtensionUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto.ImageQuestionResponse;
import com.wexl.retail.liveworksheet.dto.WorkSheetQuestionType;
import com.wexl.retail.liveworksheet.dto.WorkSheetType;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.school.AllScheduledTests;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.dto.TestDefinitionRequest;
import com.wexl.retail.test.school.dto.TestQuestionRequest;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@RequiredArgsConstructor
@Slf4j
@Service
public class LiveWorkSheetService {

  private final ContentService contentService;

  private final AuthService authService;
  private final ScheduleTestService scheduleTestService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final StrapiService strapiService;
  private final StorageService storageService;
  private final ExtensionUtil extensionUtil;
  private final TestDefinitionRepository testDefinitionRepository;
  private static final String DATE_TIME_FORMAT = "yyyyMMddhhmmssSSS";

  public void createLiveWorkSheet(TestDefinitionRequest testDefinitionRequest, String bearerToken) {
    var question = buildQuestion(testDefinitionRequest);
    contentService.createQuestion(
        question, authService.getTeacherDetails().getOrganization(), bearerToken);
  }

  private LiveWorkSheetDto.LiveWorkSheetQuestionResponse buildLiveWorkSheetQuestionResponse(
      TestDefinition testDefinition,
      List<QuestionDto.SearchQuestionResponse> searchQuestionResponses) {

    List<Question> questionsResponse = new ArrayList<>();

    List<LiveWorkSheetDto.LiveWorksheetAnswers> answers = new ArrayList<>();

    List<QuestionDto.WorkSheet> workSheets =
        searchQuestionResponses.stream()
            .map(QuestionDto.SearchQuestionResponse::questions)
            .flatMap(Collection::stream)
            .map(QuestionDto.Question::worksheet)
            .flatMap(Collection::stream)
            .toList();

    workSheets.forEach(
        worksheet -> {
          questionsResponse.add(buildQuestionResponse(worksheet, testDefinition, false));
          answers.add(
              LiveWorkSheetDto.LiveWorksheetAnswers.builder()
                  .answer(worksheet.workSheetQuestion().answer())
                  .answerUuid(worksheet.workSheetQuestion().answerUuid())
                  .answerType(worksheet.workSheetQuestion().workSheetAnswerType())
                  .build());
        });
    Collections.shuffle(answers);
    return LiveWorkSheetDto.LiveWorkSheetQuestionResponse.builder()
        .answers(answers)
        .workSheetQuestion(questionsResponse)
        .build();
  }

  public Question buildQuestionResponse(
      QuestionDto.WorkSheet workSheet, TestDefinition testDefinition, boolean withAnswer) {
    Question question = new Question();
    question.setQuestions(workSheet.workSheetQuestion().question());
    question.setActive(true);
    question.setMarks(workSheet.workSheetQuestion().marks());
    question.setId(workSheet.id().intValue());
    question.setSubjectSlug(testDefinition.getSubjectSlug());
    question.setType(workSheet.workSheetQuestion().workSheetQuestionType().name());
    question.setUuid(workSheet.uuid());
    question.setLiveWorksheetAnswerUuid(
        withAnswer ? workSheet.workSheetQuestion().answerUuid() : null);
    question.setLiveWorksheetAnswer(withAnswer ? workSheet.workSheetQuestion().answer() : null);
    question.setLiveWorksheetAnswerType(
        withAnswer ? workSheet.workSheetQuestion().workSheetAnswerType() : null);
    question.setOrganization(testDefinition.getOrganization());
    return question;
  }

  public void uploadLiveWorksheetResponseInS3(
      TestDefinition testDefinition,
      List<QuestionDto.SearchQuestionResponse> searchQuestionResponses) {
    if (searchQuestionResponses.isEmpty()) {
      return;
    }
    ObjectMapper objectMapper = new ObjectMapper();
    try {
      var contentAsBytes =
          objectMapper.writeValueAsBytes(
              buildLiveWorkSheetQuestionResponse(
                  testDefinition, new ArrayList<>(searchQuestionResponses)));
      String objectKey =
          format(
              Constants.LIVE_WORKSHEET_PATH,
              testDefinition.getOrganization(),
              testDefinition.getId());
      storageService.uploadFile(contentAsBytes, objectKey, MediaType.APPLICATION_JSON_VALUE);
      log.info("saved worksheet response in s3 path [ %s ]:".formatted(objectKey));
    } catch (JsonProcessingException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private QuestionDto.QuestionRequest buildQuestion(TestDefinitionRequest testDefinitionRequest) {
    var requests = testDefinitionRequest.getQuestions();
    return QuestionDto.QuestionRequest.builder()
        .question("")
        .marks(1)
        .category("1")
        .chapterSlug(requests.get(0).getChapterSlug())
        .active(true)
        .bloomsTaxonomyId(1)
        .organization(authService.getTeacherDetails().getOrganization())
        .type(QuestionType.LIVE_WORKSHEET)
        .subtopicSlug(requests.get(0).getSubtopicSlug())
        .worksheet(buildLiveWorkSheet(requests, testDefinitionRequest.getWorkSheetType()))
        .build();
  }

  private List<QuestionDto.WorkSheet> buildLiveWorkSheet(
      List<TestQuestionRequest> testQuestionRequests, WorkSheetType workSheetType) {

    return testQuestionRequests.stream()
        .map(
            request -> {
              var workSheetQuestion =
                  QuestionDto.WorkSheetQuestion.builder()
                      .question(request.getQuestion())
                      .answer(buildAnswer(request))
                      .answerUuid(UUID.randomUUID().toString())
                      .workSheetQuestionType(request.getQuestionType())
                      .workSheetAnswerType(request.getAnswerType())
                      .answerUuid(UUID.randomUUID().toString())
                      .marks(Objects.nonNull(request.getMarks()) ? request.getMarks() : 1)
                      .build();
              return QuestionDto.WorkSheet.builder()
                  .workSheetQuestion(workSheetQuestion)
                  .uuid(request.getQuestionUuid())
                  .workSheetType(workSheetType)
                  .build();
            })
        .toList();
  }

  private String buildAnswer(TestQuestionRequest request) {
    if (WorkSheetQuestionType.IMAGE.equals(request.getAnswerType())) {
      return request.getAnswer();
    }
    return request.getAnswer();
  }

  public List<LiveWorkSheetDto.LiveWorkSheetResponse> getStudentLiveWorkSheets(
      String orgSlug, boolean isScheduled) {
    if (isScheduled) {
      return getScheduledLiveWorkSheets();
    }
    return getStudentClassLiveWorkSheets(orgSlug);
  }

  private List<LiveWorkSheetDto.LiveWorkSheetResponse> getScheduledLiveWorkSheets() {
    try {
      long studentUserId = authService.getUserDetails().getId();
      List<AllScheduledTests> studentLiveWorkSheets =
          scheduleTestStudentRepository.findAllTestsForStudent(
              studentUserId, List.of(TestType.LIVE_WORKSHEET.name()));
      if (studentLiveWorkSheets.isEmpty()) {
        return Collections.emptyList();
      }
      return studentLiveWorkSheets.stream().map(this::buildLiveWorkSheetResponse).toList();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  private LiveWorkSheetDto.LiveWorkSheetResponse buildLiveWorkSheetResponse(
      AllScheduledTests scheduleTest) {

    return LiveWorkSheetDto.LiveWorkSheetResponse.builder()
        .testDefinitionId(scheduleTest.getTestDefinitionId())
        .scheduleTestId(scheduleTest.getScheduleTestId())
        .startDate(convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .endDate(convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .status(
            scheduleTestService.getScheduledTestStatus(
                scheduleTest.getStartDate(), scheduleTest.getEndDate()))
        .subjectName(
            scheduleTest.getSubjectSlug().isEmpty()
                ? null
                : strapiService.getSubjectBySlug(scheduleTest.getSubjectSlug()).getName())
        .testName(scheduleTest.getTestName())
        .testState(scheduleTest.getTestState())
        .testType(TestType.LIVE_WORKSHEET)
        .scheduleTestUuid(scheduleTest.getScheduleTestUuid())
        .build();
  }

  public LiveWorkSheetDto.ImageQuestionResponse uploadImageQuestion(
      String orgSlug, MultipartFile multipartFile) {

    final String reference =
        DateTimeFormatter.ofPattern(DATE_TIME_FORMAT).format(LocalDateTime.now(UTC));

    if (CONTENT_TYPE_EXTENSIONS.containsKey(multipartFile.getContentType())) {
      try {
        String originalFileName =
            "test.%s".formatted(CONTENT_TYPE_EXTENSIONS.get(multipartFile.getContentType()));
        return generateImageUrl(orgSlug, multipartFile, reference, originalFileName);
      } catch (Exception e) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
      }
    }

    // fallback on filename extension if that doesn't exist
    extensionUtil.validate(multipartFile.getOriginalFilename());
    try {
      return generateImageUrl(
          orgSlug, multipartFile, reference, multipartFile.getOriginalFilename());
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private ImageQuestionResponse generateImageUrl(
      String orgSlug, MultipartFile multipartFile, String reference, String originalFileName)
      throws IOException {
    final String url =
        generateImageUrl(multipartFile.getBytes(), reference, originalFileName, orgSlug);
    return ImageQuestionResponse.builder().path("").url(url).previewUrl(url).build();
  }

  private String probeContentType(String actualFileName) {
    try {
      return Files.probeContentType(Path.of(actualFileName));
    } catch (IOException e) {
      log.debug("Ignore this error", e);
    }
    return MediaType.IMAGE_PNG_VALUE;
  }

  public String generateImageUrl(
      byte[] imageData, String reference, String actualFileName, String orgSlug) {
    try {
      String contentType = probeContentType(actualFileName);
      var filePath = getFilePath(orgSlug, reference, contentType);
      storageService.writeFile("wexl-strapi-images", contentType, filePath, imageData, true);
      return "https://images.wexledu.com/%s".formatted(filePath);
    } catch (Exception ex) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Unable to upload the image to s3 path :" + ex.getMessage(),
          ex);
    }
  }

  public String getFilePath(String orgSlug, String reference, String contentType) {
    return "live-worksheets/%s/%s%s"
        .formatted(orgSlug, reference, ExtensionUtil.getExtension(contentType));
  }

  public LiveWorkSheetDto.LiveWorkSheetQuestionResponse getLiveWorksheetResponse(
      TestDefinition testDefinition) {
    try {
      var questionResponse =
          storageService.downloadFile(
              format(
                  Constants.LIVE_WORKSHEET_PATH,
                  testDefinition.getOrganization(),
                  testDefinition.getId()),
              LiveWorkSheetDto.LiveWorkSheetQuestionResponse.class);
      if (Objects.isNull(questionResponse)) {
        log.error("could not find live worksheet response in s3");
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "Could not load Live worksheet Questions");
      }
      return questionResponse;
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public List<LiveWorkSheetDto.LiveWorkSheetResponse> getStudentClassLiveWorkSheets(
      String orgSlug) {
    List<TestDefinition> testDefinitions =
        testDefinitionRepository
            .findTop100ByGradeSlugAndOrganizationAndTypeAndDeletedAtIsNullOrderByCreatedAtDesc(
                strapiService
                    .getGradeById(authService.getStudentDetails().getStudentInfo().getClassId())
                    .getSlug(),
                orgSlug,
                TestType.LIVE_WORKSHEET);
    if (testDefinitions.isEmpty()) {
      return Collections.emptyList();
    }
    return testDefinitions.stream().map(this::buildStudentClassLiveWorkSheetResponse).toList();
  }

  private LiveWorkSheetDto.LiveWorkSheetResponse buildStudentClassLiveWorkSheetResponse(
      TestDefinition testDefinition) {

    return LiveWorkSheetDto.LiveWorkSheetResponse.builder()
        .testDefinitionId(testDefinition.getId())
        .subjectName(
            Objects.nonNull(testDefinition.getSubjectSlug())
                    && !testDefinition.getSubjectSlug().isEmpty()
                ? strapiService.getSubjectBySlug(testDefinition.getSubjectSlug()).getName()
                : null)
        .testName(testDefinition.getTestName())
        .testType(testDefinition.getType())
        .build();
  }

  public LiveWorkSheetDto.LiveWorksheetResult getLiveWorksheetResponse(
      String orgSlug, long testDefinitionId) {
    var testDefinition =
        testDefinitionRepository
            .findByIdAndOrganization(testDefinitionId, orgSlug)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidTestDefinition"));
    var questionResponse = getLiveWorksheetResponse(testDefinition);
    return LiveWorkSheetDto.LiveWorksheetResult.builder()
        .theme(testDefinition.getTheme())
        .testName(testDefinition.getTestName())
        .liveWorkSheetQuestionResponse(questionResponse)
        .build();
  }

  public Question getLiveWorksheetQuestionByUuid(
      String bearerToken, TestDefinition testDefinition, String questionUuid) {

    var liveWorkSheet =
        contentService.getQuestionsByUuid(
            bearerToken,
            QuestionType.LIVE_WORKSHEET.name(),
            questionUuid,
            testDefinition.getOrganization());
    List<QuestionDto.WorkSheet> workSheets =
        liveWorkSheet.questions().stream()
            .map(QuestionDto.Question::worksheet)
            .flatMap(Collection::stream)
            .toList();
    return buildQuestionResponse(workSheets.getFirst(), testDefinition, true);
  }

  public List<Question> getLiveWorksheetResult(String orgSlug, long id, String bearerToken) {
    var testDefinition =
        testDefinitionRepository
            .findByIdAndOrganization(id, orgSlug)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidTestDefinition"));
    var questions =
        testDefinition.getTestDefinitionSections().get(0).getTestQuestions().stream()
            .map(
                testQuestion ->
                    getLiveWorksheetQuestionByUuid(
                        bearerToken, testDefinition, testQuestion.getQuestionUuid()))
            .toList();
    return new ArrayList<>(questions);
  }
}
