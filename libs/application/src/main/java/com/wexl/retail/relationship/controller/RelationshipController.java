package com.wexl.retail.relationship.controller;

import com.wexl.retail.relationship.dto.RelationshipDto;
import com.wexl.retail.relationship.service.RelationshipService;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@AllArgsConstructor
@RestController
@RequestMapping("/orgs/{orgSlug}")
public class RelationshipController {

  private final RelationshipService relationshipService;

  @PostMapping("/students/{studentAuthId}/relationships")
  public void saveStudentRelationship(
      @RequestBody RelationshipDto.Request request, @PathVariable String studentAuthId) {
    relationshipService.saveStudentRelationship(studentAuthId, request);
  }

  @GetMapping("/students/{studentAuthId}/relationships")
  public List<RelationshipDto.Response> getStudentRelationship(@PathVariable String studentAuthId) {
    return relationshipService.getStudentRelationship(studentAuthId);
  }

  @GetMapping("/relationships")
  public List<RelationshipDto.Types> getRelationshipTypes() {
    return relationshipService.getRelationshipTypes();
  }
}
